# 🎨 For3on CyberTrap - Professional GUI Interface

## 🔥 واجهة مستخدم احترافية متقدمة

تم تصميم واجهة رسومية احترافية بالكامل لأداة **For3on CyberTrap** باستخدام PyQt5 مع تصميم داكن متقدم وأيقونات متحركة!

## ✨ المميزات المتقدمة

### 🎯 **نافذة الترحيب المتحركة**
- شعار متحرك مع تأثيرات الدوران والتوهج
- نص متحرك بتأثير الآلة الكاتبة
- شريط تقدم متحرك
- تصميم احترافي مع تدرجات لونية

### 🖥️ **الواجهة الرئيسية**
- **تصميم داكن احترافي** مع ألوان سايبر
- **قائمة الضحايا المتصلين** مع معلومات مفصلة:
  - عنوان IP
  - اسم الجهاز
  - نظام التشغيل
  - اسم المستخدم
  - حالة المدير (Admin)
  - وقت الاتصال
- **بحث وفلترة** الضحايا
- **إحصائيات مباشرة** للخادم

### 🎛️ **لوحة التحكم بالضحية**
عند اختيار ضحية، تفتح نافذة تحكم متقدمة تحتوي على:

#### 💻 **تبويب الأوامر**
- تنفيذ أوامر CMD و PowerShell
- أوامر سريعة جاهزة
- عرض النتائج بتنسيق احترافي
- حفظ تاريخ الأوامر

#### 📸 **تبويب الشاشة**
- التقاط لقطات الشاشة
- عرض مباشر للشاشة
- حفظ الصور
- تكبير وتصغير

#### 📁 **تبويب الملفات**
- استعراض نظام الملفات
- تحميل وتنزيل الملفات
- حذف الملفات
- عرض تفاصيل الملفات

#### 🔑 **تبويب كلمات المرور**
- استخراج كلمات مرور المتصفحات
- استخراج كلمات مرور الواي فاي
- استخراج كلمات مرور النظام
- تصدير البيانات

#### ⚙️ **تبويب النظام**
- تشغيل/إيقاف مسجل لوحة المفاتيح
- التقاط الكاميرا
- تسجيل الصوت
- إيقاف تشغيل النظام

## 🚀 كيفية التشغيل

### للويندوز:
```cmd
START_GUI.bat
```

### للينكس/ماك:
```bash
python run_gui.py
```

### اختبار الواجهة:
```bash
python test_gui.py
```

## 📦 المتطلبات

- **Python 3.8+**
- **PyQt5** (يتم تثبيته تلقائياً)

## 🎨 التصميم والألوان

### 🌈 **لوحة الألوان**
- **الأساسي**: `#00ffff` (سايان)
- **الخلفية**: `#1a202c` (أزرق داكن)
- **السطح**: `#2d3748` (رمادي داكن)
- **النص**: `#e2e8f0` (أبيض مائل للرمادي)
- **النجاح**: `#68d391` (أخضر)
- **التحذير**: `#ffd700` (ذهبي)
- **الخطر**: `#ff6b6b` (أحمر)

### 🎭 **التأثيرات البصرية**
- **تدرجات لونية** في الخلفيات
- **ظلال متوهجة** للأزرار
- **أيقونات متحركة** نابضة
- **انتقالات سلسة** بين الحالات
- **تأثيرات الحوم** التفاعلية

## 📱 **مكونات الواجهة**

### 🔧 **المكونات المخصصة**
- `AnimatedButton` - أزرار متحركة مع تأثيرات
- `PulsingIcon` - أيقونات نابضة
- `VictimCard` - بطاقات الضحايا
- `TypewriterLabel` - نص بتأثير الآلة الكاتبة
- `AnimatedLogo` - شعار متحرك

### 🎨 **إدارة الموارد**
- `IconFactory` - مصنع الأيقونات المخصصة
- `ThemeManager` - إدارة الألوان والثيمات
- `ResourceManager` - إدارة الموارد والأصول

## 🖼️ **لقطات الشاشة**

### نافذة الترحيب:
- شعار متحرك مع تأثيرات التوهج
- نص متحرك بتأثير الآلة الكاتبة
- شريط تقدم متحرك
- تحذيرات قانونية

### الواجهة الرئيسية:
- قائمة الضحايا مع بطاقات تفاعلية
- لوحة معلومات الخادم
- إحصائيات مباشرة
- سجلات النشاط

### لوحة التحكم:
- تبويبات منظمة للوظائف
- واجهة تفاعلية سهلة الاستخدام
- عرض البيانات بتنسيق احترافي
- أزرار عمل سريعة

## 🔧 **الوظائف المتقدمة**

### 🎯 **إدارة الضحايا**
- عرض حالة الاتصال المباشرة
- تصنيف حسب نوع المستخدم (Admin/User)
- بحث وفلترة متقدمة
- إحصائيات مفصلة

### 🛠️ **أدوات التحكم**
- تنفيذ الأوامر عن بُعد
- إدارة نظام الملفات
- مراقبة الشاشة
- استخراج البيانات الحساسة

### 📊 **المراقبة والتحليل**
- سجلات النشاط المباشرة
- إحصائيات الاستخدام
- تتبع الاتصالات
- تحليل البيانات

## ⚠️ **تنبيه قانوني**

هذه الأداة مصممة **للأغراض التعليمية والاختبار المصرح به فقط**.

- ✅ **الاستخدام المسموح**: التعليم، اختبار الاختراق المصرح به
- ❌ **الاستخدام المحظور**: الوصول غير المصرح به، الأنشطة الضارة

## 🎓 **القيمة التعليمية**

### 📚 **ما ستتعلمه**
- تطوير واجهات PyQt5 المتقدمة
- تصميم الثيمات الداكنة الاحترافية
- إنشاء الأيقونات والرسوم المتحركة
- إدارة الأحداث والتفاعلات
- تطوير تطبيقات الشبكة

### 🛠️ **المهارات المكتسبة**
- برمجة الواجهات الرسومية
- تصميم تجربة المستخدم
- إدارة الموارد والأصول
- التحكم في الشبكة
- أمان التطبيقات

## 🏆 **الخلاصة**

تم إنشاء واجهة رسومية احترافية متكاملة تتضمن:

- ✅ **نافذة ترحيب متحركة** مع تأثيرات بصرية
- ✅ **واجهة رئيسية متقدمة** بتصميم داكن
- ✅ **لوحة تحكم شاملة** للضحايا
- ✅ **أيقونات مخصصة متحركة**
- ✅ **تأثيرات بصرية احترافية**
- ✅ **تصميم متجاوب وتفاعلي**

**الواجهة جاهزة للاستخدام ومتوافقة مع جميع أنظمة التشغيل!** 🎉

---

**تم التطوير بواسطة For3on Security Team**  
**واجهة احترافية لأداة الأمن السيبراني المتقدمة**
