# CyberTrap For3on - Installation Guide

## 🚀 Quick Start

### 1. Prerequisites
- **Python 3.8+** (Download from [python.org](https://python.org))
- **Windows 10/11** (Recommended for full functionality)
- **Administrator privileges** (For some advanced features)

### 2. Installation Steps

#### Option A: Automated Setup (Recommended)
```bash
# Clone the repository
git clone https://github.com/for3on/cybertrap-for3on.git
cd cybertrap-for3on

# Run automated setup
python setup.py
```

#### Option B: Manual Setup
```bash
# Clone the repository
git clone https://github.com/for3on/cybertrap-for3on.git
cd cybertrap-for3on

# Install dependencies
pip install -r requirements.txt

# Create directories
mkdir logs output server\assets

# Run the server
python run_server.py
```

### 3. Quick Launch
```bash
# Start server GUI
python run_server.py

# Or use batch file (Windows)
start_cybertrap.bat

# Test client (for development)
python run_client.py
```

## 📦 Dependencies

### Core Dependencies
- **PyQt5** - GUI framework
- **cryptography** - Encryption support
- **psutil** - System information
- **pillow** - Image processing
- **pyautogui** - Screenshot capture
- **pynput** - Keylogger functionality
- **requests** - HTTP requests
- **opencv-python** - Video capture

### Optional Dependencies
- **pyinstaller** - For payload compilation
- **upx** - For executable compression

## 🔧 Configuration

### Server Configuration
Edit `shared/config.py` to customize:
```python
SERVER_HOST = "0.0.0.0"      # Listen on all interfaces
SERVER_PORT = 4444           # Default port
MAX_CONNECTIONS = 100        # Maximum clients
ENCRYPTION_ENABLED = True    # Enable encryption
```

### Client Configuration
Generated automatically during payload build with custom settings.

## 🛠️ Development Setup

### For Developers
```bash
# Install development dependencies
pip install -r requirements.txt
pip install pyinstaller upx

# Set up pre-commit hooks (optional)
pip install pre-commit
pre-commit install

# Run tests
python -m pytest tests/
```

### Project Structure
```
CyberTrap_For3on/
├── server/           # Server application
├── client/           # Client application  
├── shared/           # Shared modules
├── builder/          # Payload builder
├── tests/            # Test suite
├── docs/             # Documentation
└── examples/         # Usage examples
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Import Errors
```bash
# Solution: Install missing dependencies
pip install -r requirements.txt
```

#### 2. PyQt5 Installation Issues
```bash
# Windows
pip install PyQt5

# Linux
sudo apt-get install python3-pyqt5

# macOS
brew install pyqt5
```

#### 3. Permission Errors
- Run as Administrator on Windows
- Use `sudo` on Linux/macOS for system-level features

#### 4. Firewall Issues
- Add exception for Python in Windows Firewall
- Configure port forwarding if needed

### Getting Help
1. Check the [README.md](README.md) for detailed documentation
2. Review the [troubleshooting section](#troubleshooting)
3. Create an issue on GitHub with error details
4. Contact the development team

## 🔒 Security Notes

### Before Using
1. **Legal Authorization**: Ensure you have permission to test on target systems
2. **Isolated Environment**: Use in isolated networks or VMs
3. **Antivirus**: May be detected as malware (expected behavior)
4. **Responsible Use**: Follow ethical guidelines and laws

### Network Security
- Use encrypted connections only
- Monitor network traffic during testing
- Implement proper access controls
- Regular security assessments

## 📚 Additional Resources

- **Documentation**: See `docs/` directory
- **Examples**: Check `examples/` directory  
- **API Reference**: Available in source code comments
- **Video Tutorials**: Coming soon

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the Educational License - see [LICENSE](LICENSE) for details.

## ⚠️ Legal Disclaimer

**IMPORTANT**: This tool is for educational and authorized testing only. 
Unauthorized access to computer systems is illegal. Users are responsible 
for compliance with all applicable laws and regulations.

---

**Happy Learning and Stay Ethical! 🛡️**
