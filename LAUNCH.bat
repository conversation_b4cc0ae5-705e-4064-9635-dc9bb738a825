@echo off
setlocal enabledelayedexpansion
title CyberTrap For3on - Universal Launcher
color 0A

:start
cls

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo.
    echo ╔══════════════════════════════════════════════════════════════════════════════╗
    echo ║                                  ERROR                                       ║
    echo ║                                                                              ║
    echo ║  Python is not installed or not in PATH                                     ║
    echo ║                                                                              ║
    echo ║  Please install Python 3.8+ from:                                          ║
    echo ║  https://python.org                                                         ║
    echo ║                                                                              ║
    echo ║  Make sure to check "Add Python to PATH" during installation               ║
    echo ║                                                                              ║
    echo ╚══════════════════════════════════════════════════════════════════════════════╝
    echo.
    pause
    exit /b 1
)

REM Check if we're in the right directory
if not exist "shared\config.py" (
    echo.
    echo ╔══════════════════════════════════════════════════════════════════════════════╗
    echo ║                                  ERROR                                       ║
    echo ║                                                                              ║
    echo ║  Please run this script from the CyberTrap project root directory          ║
    echo ║                                                                              ║
    echo ║  Make sure you're in the directory containing:                              ║
    echo ║  - shared\                                                                   ║
    echo ║  - server\                                                                   ║
    echo ║  - client\                                                                   ║
    echo ║                                                                              ║
    echo ╚══════════════════════════════════════════════════════════════════════════════╝
    echo.
    pause
    exit /b 1
)

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║   ██████╗██╗   ██╗██████╗ ███████╗██████╗ ████████╗██████╗  █████╗ ██████╗   ║
echo ║  ██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝██╔══██╗╚══██╔══╝██╔══██╗██╔══██╗██╔══██╗  ║
echo ║  ██║      ╚████╔╝ ██████╔╝█████╗  ██████╔╝   ██║   ██████╔╝███████║██████╔╝  ║
echo ║  ██║       ╚██╔╝  ██╔══██╗██╔══╝  ██╔══██╗   ██║   ██╔══██╗██╔══██║██╔═══╝   ║
echo ║  ╚██████╗   ██║   ██████╔╝███████╗██║  ██║   ██║   ██║  ██║██║  ██║██║       ║
echo ║   ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝   ╚═╝   ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝       ║
echo ║                                                                              ║
echo ║                              FOR3ON SECURITY TEAM                           ║
echo ║                     Advanced RAT for Cybersecurity Defense                  ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

REM Show Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo [INFO] Python Version: %PYTHON_VERSION%
echo [INFO] Current Directory: %CD%
echo.

echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                              QUICK ACTIONS                                   ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo  🚀 Quick Start:
echo    1. Quick Demo (Test core functionality)
echo    2. Install Dependencies
echo    3. Full System Test
echo.
echo  🖥️  Server Options:
echo    4. Start Console Server
echo    5. Start GUI Server
echo.
echo  🔧 Client Options:
echo    6. Start Simple Client
echo    7. Start Full Client
echo.
echo  🛠️  Tools:
echo    8. Connection Test
echo    9. Development Tools
echo   10. Universal Launcher (All options)
echo.
echo   0. Exit
echo.

set /p choice="Enter your choice (0-10): "

if "%choice%"=="0" (
    echo.
    echo [INFO] Thank you for using CyberTrap For3on!
    echo [INFO] Stay safe and hack ethically! 🛡️
    goto :exit
)

if "%choice%"=="1" (
    echo.
    echo [INFO] Running quick demo...
    python quick_demo.py
    goto :end
)

if "%choice%"=="2" (
    echo.
    echo [INFO] Installing basic dependencies...
    python install_basic.py
    goto :end
)

if "%choice%"=="3" (
    echo.
    echo [INFO] Running full system test...
    python test_full_system.py
    goto :end
)

if "%choice%"=="4" (
    echo.
    echo [INFO] Starting console server...
    echo [TIP] Type 'help' for available commands
    echo [TIP] Type 'start' to begin listening for connections
    echo.
    python server_console.py
    goto :end
)

if "%choice%"=="5" (
    echo.
    echo [INFO] Starting GUI server...
    if exist "run_server.py" (
        python run_server.py
    ) else (
        echo [ERROR] GUI server not available
        echo [TIP] Try console server instead (option 4)
    )
    goto :end
)

if "%choice%"=="6" (
    echo.
    set /p server_host="Server host (default: 127.0.0.1): "
    if "!server_host!"=="" set server_host=127.0.0.1
    
    set /p server_port="Server port (default: 4444): "
    if "!server_port!"=="" set server_port=4444
    
    echo [INFO] Connecting to !server_host!:!server_port!...
    python client_simple.py !server_host! !server_port!
    goto :end
)

if "%choice%"=="7" (
    echo.
    echo [INFO] Starting full client...
    if exist "run_client.py" (
        python run_client.py
    ) else (
        echo [ERROR] Full client not available
        echo [TIP] Try simple client instead (option 6)
    )
    goto :end
)

if "%choice%"=="8" (
    echo.
    echo [INFO] Connection Test Options:
    echo   1. Test server port
    echo   2. Start test server
    echo   3. Test client connection
    echo   4. Network diagnostics
    echo.
    set /p test_choice="Choose test (1-4): "
    
    if "!test_choice!"=="1" (
        set /p test_host="Host (default: 127.0.0.1): "
        if "!test_host!"=="" set test_host=127.0.0.1
        set /p test_port="Port (default: 4444): "
        if "!test_port!"=="" set test_port=4444
        python test_connection.py check !test_host! !test_port!
    ) else if "!test_choice!"=="2" (
        python test_connection.py server
    ) else if "!test_choice!"=="3" (
        set /p test_host="Host (default: 127.0.0.1): "
        if "!test_host!"=="" set test_host=127.0.0.1
        set /p test_port="Port (default: 4444): "
        if "!test_port!"=="" set test_port=4444
        python test_connection.py client !test_host! !test_port!
    ) else if "!test_choice!"=="4" (
        python test_connection.py diag
    ) else (
        echo [ERROR] Invalid test choice
    )
    goto :end
)

if "%choice%"=="9" (
    echo.
    echo [INFO] Development Tools:
    echo   1. Clean project
    echo   2. Check dependencies
    echo   3. Run tests
    echo   4. Build executable
    echo   5. Project statistics
    echo.
    set /p dev_choice="Choose tool (1-5): "
    
    if "!dev_choice!"=="1" (
        python dev_tools.py clean
    ) else if "!dev_choice!"=="2" (
        python dev_tools.py deps
    ) else if "!dev_choice!"=="3" (
        python dev_tools.py test
    ) else if "!dev_choice!"=="4" (
        python dev_tools.py build
    ) else if "!dev_choice!"=="5" (
        python dev_tools.py stats
    ) else (
        echo [ERROR] Invalid tool choice
    )
    goto :end
)

if "%choice%"=="10" (
    echo.
    echo [INFO] Starting universal launcher...
    python start.py
    goto :end
)

echo.
echo [ERROR] Invalid choice. Please enter 0-10.
pause
goto :start

:end
echo.
echo ===============================================================================
echo [INFO] Operation completed
echo [TIP] Read QUICKSTART.md for more information
echo [TIP] Run this script again to access more options
echo ===============================================================================
echo.
set /p continue="Press Enter to return to menu or type 'exit' to quit: "
if /i "!continue!"=="exit" goto :exit
goto :start

:exit
exit /b 0
