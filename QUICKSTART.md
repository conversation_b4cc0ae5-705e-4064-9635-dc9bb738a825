# 🔥 CyberTrap For3on - Quick Start Guide

## ⚡ Super Quick Start (5 minutes)

### 1. Basic Setup
```bash
# Clone or download the project
cd cybertrap-for3on

# Test core functionality (no dependencies needed)
python quick_demo.py
```

### 2. Install Basic Dependencies
```bash
# Install essential packages only
python install_basic.py

# OR install all dependencies
pip install -r requirements.txt
```

### 3. Test the System
```bash
# Run full system test
python test_full_system.py

# Choose option 1 for basic test
# Choose option 2 for interactive test
```

## 🚀 Running the RAT

### Option A: Console Mode (Recommended for Testing)
```bash
# Terminal 1: Start console server
python server_console.py

# Terminal 2: Start simple client
python client_simple.py
```

### Option B: GUI Mode (Requires PyQt5)
```bash
# Start GUI server
python run_server.py

# Build custom payload
# Use the GUI Payload Builder
```

## 📖 Basic Usage

### Server Commands (Console Mode)
```
start          - Start the server
clients        - List connected clients
select <id>    - Select a client
cmd <command>  - Execute command
screenshot     - Take screenshot
quit           - Exit
```

### Example Session
```
CyberTrap> start
✅ Server started successfully

CyberTrap> clients
👥 Connected Clients (1):
client_123      DESKTOP-ABC\user    *************   Windows

CyberTrap> select client_123
✅ Selected client: client_123

CyberTrap[client_123]> cmd whoami
✅ Command sent to client_123

CyberTrap[client_123]> screenshot
✅ Command sent to client_123

CyberTrap[client_123]> quit
```

## 🔧 Troubleshooting

### Common Issues

**Import Errors:**
```bash
# Install missing dependencies
python install_basic.py
```

**Connection Issues:**
```bash
# Test connectivity
python test_connection.py check 127.0.0.1 4444
```

**Permission Errors:**
- Run as Administrator on Windows
- Use `sudo` on Linux/macOS

### Testing Without Dependencies
```bash
# Core functionality test (no external deps)
python quick_demo.py

# Simple server test
python test_connection.py server

# Simple client test
python test_connection.py client
```

## 📁 Project Structure
```
CyberTrap_For3on/
├── 🚀 Quick Start Files
│   ├── quick_demo.py          # Test core functionality
│   ├── install_basic.py       # Install dependencies
│   ├── test_full_system.py    # Full system test
│   ├── server_console.py      # Console server
│   └── client_simple.py       # Simple client
├── 🖥️ Full Application
│   ├── run_server.py          # GUI server launcher
│   ├── run_client.py          # Full client launcher
│   ├── server/                # Server application
│   ├── client/                # Client application
│   └── shared/                # Shared modules
└── 📚 Documentation
    ├── README.md              # Full documentation
    ├── INSTALL.md             # Installation guide
    └── LICENSE                # License information
```

## ⚠️ Important Notes

### Legal Disclaimer
- **Educational use only**
- **Authorized testing only**
- **Obtain proper permission**
- **Follow local laws**

### Security Notes
- Use in isolated environments
- Monitor network traffic
- May be detected by antivirus
- Implement proper access controls

## 🎯 Next Steps

1. **Learn the Basics:**
   - Run `python quick_demo.py`
   - Read the full README.md

2. **Test Functionality:**
   - Use console mode for testing
   - Try different commands
   - Monitor logs and output

3. **Advanced Usage:**
   - Build custom payloads
   - Use GUI interface
   - Implement custom modules

4. **Security Research:**
   - Analyze network traffic
   - Study detection methods
   - Develop countermeasures

## 🆘 Getting Help

- **Quick Test:** `python quick_demo.py`
- **Full Test:** `python test_full_system.py`
- **Documentation:** `README.md`
- **Issues:** Create GitHub issue

---

**Happy Learning! 🛡️**

*Remember: Use responsibly and ethically!*
