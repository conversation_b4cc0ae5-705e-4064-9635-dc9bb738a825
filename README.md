# CyberTrap For3on - Advanced RAT for Cybersecurity Defense

![CyberTrap Logo](https://img.shields.io/badge/CyberTrap-For3on-blue?style=for-the-badge)
![Python](https://img.shields.io/badge/Python-3.8+-green?style=for-the-badge&logo=python)
![PyQt5](https://img.shields.io/badge/PyQt5-GUI-orange?style=for-the-badge)
![License](https://img.shields.io/badge/License-Educational-red?style=for-the-badge)

## 🔐 Overview

**CyberTrap For3on** is an advanced Remote Access Tool (RAT) designed specifically for cybersecurity defense purposes. This tool enables security professionals to analyze and understand attack vectors by simulating real-world RAT behavior in controlled environments.

## ⚠️ Legal Disclaimer

**IMPORTANT**: This tool is developed for educational and legitimate cybersecurity defense purposes only. Users are responsible for ensuring compliance with all applicable laws and regulations. Unauthorized access to computer systems is illegal and unethical.

## 🚀 Features

### 🖥️ Server Features
- **Modern PyQt5 GUI** with dark theme
- **Multi-client management** with threaded connections
- **Real-time client monitoring** and status tracking
- **Encrypted communications** using AES-256 + RSA
- **Comprehensive logging** and activity tracking
- **Payload builder** with customization options

### 🔧 Client Capabilities
- **System Information Gathering**
  - Hardware and software inventory
  - Network configuration
  - Running processes and services
  - Startup programs analysis

- **Remote Command Execution**
  - CMD and PowerShell support
  - Real-time output capture
  - Command history tracking

- **File System Operations**
  - Browse remote directories
  - Upload/download files
  - File management operations

- **Screen Monitoring**
  - Screenshot capture
  - Multi-monitor support
  - Image compression and transmission

- **Keylogger Functionality**
  - Advanced keystroke logging
  - Window context tracking
  - Formatted output generation

- **Credential Extraction**
  - Browser password extraction (Chrome, Firefox, Edge)
  - WiFi password recovery
  - Mimikatz integration (placeholder)

- **Audio/Video Capture**
  - Microphone recording
  - Webcam image capture
  - Media file transmission

- **Persistence Mechanisms**
  - Registry startup entries
  - Scheduled tasks
  - Windows service installation
  - WMI event subscriptions

## 📁 Project Structure

```
CyberTrap_For3on/
├── server/                 # Server application
│   ├── main.py            # Main GUI application
│   ├── server_core.py     # Core server functionality
│   ├── ui/                # User interface modules
│   │   ├── main_window.py
│   │   ├── client_control_window.py
│   │   └── payload_builder_dialog.py
│   └── assets/            # GUI assets
├── client/                # Client application
│   ├── client.py          # Main client
│   └── modules/           # Client modules
│       ├── system_info.py
│       ├── screenshot.py
│       ├── keylogger.py
│       ├── file_manager.py
│       ├── browser_passwords.py
│       ├── wifi_passwords.py
│       ├── microphone.py
│       ├── webcam.py
│       └── persistence.py
├── shared/                # Shared modules
│   ├── config.py          # Configuration management
│   ├── encryption.py      # Encryption utilities
│   └── protocol.py        # Communication protocol
├── builder/               # Payload builder
│   └── payload_builder.py
├── requirements.txt       # Python dependencies
└── README.md             # This file
```

## 🛠️ Installation

### Prerequisites
- Python 3.8 or higher
- Windows 10/11 (for full functionality)
- Administrator privileges (for some features)

### Setup Instructions

1. **Clone the repository**
   ```bash
   git clone https://github.com/for3on/cybertrap-for3on.git
   cd cybertrap-for3on
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Install additional tools (optional)**
   ```bash
   # For payload compilation
   pip install pyinstaller
   
   # For executable compression
   # Download UPX from https://upx.github.io/
   ```

## 🚀 Usage

### Starting the Server

1. **Run the server GUI**
   ```bash
   cd server
   python main.py
   ```

2. **Configure server settings**
   - Set listening host and port
   - Configure encryption settings
   - Adjust security options

3. **Start the server**
   - Click "Start Server" button
   - Monitor the logs for connections

### Building Client Payloads

1. **Open Payload Builder**
   - Use the GUI menu: Tools → Payload Builder
   - Or run directly: `python builder/payload_builder.py`

2. **Configure payload settings**
   - **Connection**: Server host, port, intervals
   - **Persistence**: Startup methods, installation paths
   - **Stealth**: Anti-VM, anti-debug, obfuscation
   - **Build**: Output settings, compression

3. **Generate payload**
   - Click "Build Payload"
   - Wait for compilation to complete
   - Retrieve the generated executable

### Client Operations

Once a client connects, you can:

- **View system information**
- **Execute remote commands**
- **Browse and manage files**
- **Capture screenshots**
- **Monitor keystrokes**
- **Extract credentials**
- **Record audio/video**

## 🔒 Security Features

### Encryption
- **AES-256-CBC** for data encryption
- **RSA-2048** for key exchange
- **Base64 encoding** for data transmission
- **Secure key generation** and management

### Stealth Capabilities
- **Process hiding** and console concealment
- **Anti-VM detection** to avoid analysis
- **Anti-debug protection** against reverse engineering
- **String encryption** to obfuscate code
- **Fake error messages** for social engineering

### Persistence Methods
- **Registry startup** entries
- **Scheduled tasks** with system privileges
- **Windows services** for system-level persistence
- **WMI event subscriptions** for advanced persistence

## 🧪 Testing Environment

### Recommended Setup
1. **Isolated network** or virtual environment
2. **Test machines** with various Windows versions
3. **Network monitoring** tools for traffic analysis
4. **Antivirus testing** with multiple engines

### Testing Scenarios
- **Red team exercises** and penetration testing
- **Malware analysis** and reverse engineering
- **Security awareness** training
- **Incident response** practice

## 📊 Monitoring and Logging

### Server Logs
- Client connection events
- Command execution history
- File transfer activities
- Error and warning messages

### Client Logs
- System information changes
- Keystroke recordings
- Screenshot captures
- Credential extraction results

## 🔧 Configuration

### Server Configuration
```python
# shared/config.py
SERVER_HOST = "0.0.0.0"
SERVER_PORT = 4444
MAX_CONNECTIONS = 100
ENCRYPTION_ENABLED = True
```

### Client Configuration
```python
# Generated during payload build
CONFIG = {
    "SERVER_HOST": "127.0.0.1",
    "SERVER_PORT": 4444,
    "RECONNECT_INTERVAL": 30,
    "HEARTBEAT_INTERVAL": 60,
    "PERSISTENCE": {...},
    "STEALTH": {...}
}
```

## 🛡️ Defense Recommendations

### Detection Methods
- **Network monitoring** for suspicious traffic
- **Process monitoring** for unusual behavior
- **Registry monitoring** for persistence mechanisms
- **File system monitoring** for unauthorized changes

### Prevention Strategies
- **Application whitelisting** and execution control
- **Network segmentation** and access control
- **User education** and security awareness
- **Regular security assessments** and updates

## 🤝 Contributing

This project is for educational purposes. If you wish to contribute:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📄 License

This project is licensed under the Educational License - see the [LICENSE](LICENSE) file for details.

## ⚠️ Ethical Use

By using this software, you agree to:
- Use it only for legitimate security research and education
- Obtain proper authorization before testing on any systems
- Comply with all applicable laws and regulations
- Not use it for malicious or illegal purposes

## 📞 Support

For questions or support:
- Create an issue on GitHub
- Contact the development team
- Review the documentation

## 🙏 Acknowledgments

- **For3on Security Team** for development and testing
- **Cybersecurity community** for research and feedback
- **Open source projects** that made this possible

---

**Remember**: With great power comes great responsibility. Use this tool ethically and legally.
