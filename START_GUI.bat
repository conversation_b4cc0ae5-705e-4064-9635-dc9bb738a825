@echo off
setlocal enabledelayedexpansion
title For3on CyberTrap - Professional RAT GUI
color 0B

cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║   ██████╗██╗   ██╗██████╗ ███████╗██████╗ ████████╗██████╗  █████╗ ██████╗   ║
echo ║  ██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝██╔══██╗╚══██╔══╝██╔══██╗██╔══██╗██╔══██╗  ║
echo ║  ██║      ╚████╔╝ ██████╔╝█████╗  ██████╔╝   ██║   ██████╔╝███████║██████╔╝  ║
echo ║  ██║       ╚██╔╝  ██╔══██╗██╔══╝  ██╔══██╗   ██║   ██╔══██╗██╔══██║██╔═══╝   ║
echo ║  ╚██████╗   ██║   ██████╔╝███████╗██║  ██║   ██║   ██║  ██║██║  ██║██║       ║
echo ║   ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝   ╚═╝   ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝       ║
echo ║                                                                              ║
echo ║                         PROFESSIONAL RAT INTERFACE                          ║
echo ║                              FOR3ON SECURITY TEAM                           ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo [INFO] Please install Python 3.8+ from https://python.org
    echo [INFO] Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

REM Show Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo [INFO] Python Version: %PYTHON_VERSION%
echo [INFO] Starting For3on CyberTrap GUI...
echo.

REM Check PyQt5
echo [INFO] Checking PyQt5...
python -c "import PyQt5; print('PyQt5 is available')" >nul 2>&1
if errorlevel 1 (
    echo [WARNING] PyQt5 is not installed
    echo [INFO] Installing PyQt5...
    python -m pip install PyQt5 --user
    if errorlevel 1 (
        echo [ERROR] Failed to install PyQt5
        echo [INFO] Please install manually: pip install PyQt5
        pause
        exit /b 1
    )
    echo [SUCCESS] PyQt5 installed successfully
)

echo [INFO] All dependencies are ready
echo [INFO] Launching GUI interface...
echo.

REM Launch the GUI
python run_gui.py

if errorlevel 1 (
    echo.
    echo [ERROR] GUI failed to start
    echo [INFO] Check the error messages above
    echo [INFO] You can also try: python test_gui.py
    echo.
    pause
) else (
    echo.
    echo [INFO] GUI closed successfully
)

exit /b 0
