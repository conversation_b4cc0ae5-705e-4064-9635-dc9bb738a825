# -*- coding: utf-8 -*-
"""
CyberTrap For3on - File Manager Module
File system operations and management
"""

import os
import base64
import shutil
import stat
import time
from pathlib import Path

class FileManager:
    """File system operations manager"""
    
    def __init__(self):
        self.max_file_size = 50 * 1024 * 1024  # 50MB limit
    
    def list_directory(self, path):
        """List directory contents"""
        try:
            path = Path(path)
            
            if not path.exists():
                raise Exception(f"Path does not exist: {path}")
            
            if not path.is_dir():
                raise Exception(f"Path is not a directory: {path}")
            
            items = []
            
            # Add parent directory entry
            if path.parent != path:
                items.append({
                    "name": "..",
                    "type": "directory",
                    "path": str(path.parent),
                    "size": 0,
                    "modified": 0,
                    "permissions": "drwxr-xr-x"
                })
            
            # List directory contents
            for item in path.iterdir():
                try:
                    stat_info = item.stat()
                    
                    item_info = {
                        "name": item.name,
                        "type": "directory" if item.is_dir() else "file",
                        "path": str(item),
                        "size": stat_info.st_size if item.is_file() else 0,
                        "modified": stat_info.st_mtime,
                        "permissions": self._get_permissions(stat_info.st_mode)
                    }
                    
                    # Add file extension for files
                    if item.is_file():
                        item_info["extension"] = item.suffix.lower()
                    
                    items.append(item_info)
                    
                except (PermissionError, OSError):
                    # Skip items we can't access
                    continue
            
            # Sort: directories first, then files
            items.sort(key=lambda x: (x["type"] != "directory", x["name"].lower()))
            
            return {
                "path": str(path),
                "items": items,
                "total_items": len(items)
            }
            
        except Exception as e:
            raise Exception(f"Failed to list directory: {str(e)}")
    
    def _get_permissions(self, mode):
        """Convert file mode to permission string"""
        permissions = ""
        
        # File type
        if stat.S_ISDIR(mode):
            permissions += "d"
        elif stat.S_ISLNK(mode):
            permissions += "l"
        else:
            permissions += "-"
        
        # Owner permissions
        permissions += "r" if mode & stat.S_IRUSR else "-"
        permissions += "w" if mode & stat.S_IWUSR else "-"
        permissions += "x" if mode & stat.S_IXUSR else "-"
        
        # Group permissions
        permissions += "r" if mode & stat.S_IRGRP else "-"
        permissions += "w" if mode & stat.S_IWGRP else "-"
        permissions += "x" if mode & stat.S_IXGRP else "-"
        
        # Other permissions
        permissions += "r" if mode & stat.S_IROTH else "-"
        permissions += "w" if mode & stat.S_IWOTH else "-"
        permissions += "x" if mode & stat.S_IXOTH else "-"
        
        return permissions
    
    def download_file(self, file_path):
        """Download file and return as base64"""
        try:
            path = Path(file_path)
            
            if not path.exists():
                raise Exception(f"File does not exist: {path}")
            
            if not path.is_file():
                raise Exception(f"Path is not a file: {path}")
            
            # Check file size
            file_size = path.stat().st_size
            if file_size > self.max_file_size:
                raise Exception(f"File too large: {file_size} bytes (max: {self.max_file_size})")
            
            # Read file
            with open(path, 'rb') as f:
                file_data = f.read()
            
            # Convert to base64
            base64_data = base64.b64encode(file_data).decode('utf-8')
            
            return {
                "filename": path.name,
                "path": str(path),
                "size": file_size,
                "data": base64_data,
                "modified": path.stat().st_mtime
            }
            
        except Exception as e:
            raise Exception(f"Failed to download file: {str(e)}")
    
    def upload_file(self, file_path, base64_data, overwrite=False):
        """Upload file from base64 data"""
        try:
            path = Path(file_path)
            
            # Check if file exists
            if path.exists() and not overwrite:
                raise Exception(f"File already exists: {path}")
            
            # Create directory if it doesn't exist
            path.parent.mkdir(parents=True, exist_ok=True)
            
            # Decode base64 data
            try:
                file_data = base64.b64decode(base64_data)
            except Exception as e:
                raise Exception(f"Invalid base64 data: {str(e)}")
            
            # Write file
            with open(path, 'wb') as f:
                f.write(file_data)
            
            return {
                "filename": path.name,
                "path": str(path),
                "size": len(file_data),
                "uploaded": True
            }
            
        except Exception as e:
            raise Exception(f"Failed to upload file: {str(e)}")
    
    def delete_file(self, file_path):
        """Delete file or directory"""
        try:
            path = Path(file_path)
            
            if not path.exists():
                raise Exception(f"Path does not exist: {path}")
            
            if path.is_file():
                path.unlink()
                return {"deleted": str(path), "type": "file"}
            elif path.is_dir():
                shutil.rmtree(path)
                return {"deleted": str(path), "type": "directory"}
            else:
                raise Exception(f"Unknown path type: {path}")
                
        except Exception as e:
            raise Exception(f"Failed to delete: {str(e)}")
    
    def create_directory(self, dir_path):
        """Create directory"""
        try:
            path = Path(dir_path)
            path.mkdir(parents=True, exist_ok=True)
            
            return {
                "created": str(path),
                "type": "directory"
            }
            
        except Exception as e:
            raise Exception(f"Failed to create directory: {str(e)}")
    
    def move_file(self, source_path, destination_path):
        """Move/rename file or directory"""
        try:
            source = Path(source_path)
            destination = Path(destination_path)
            
            if not source.exists():
                raise Exception(f"Source does not exist: {source}")
            
            # Create destination directory if needed
            destination.parent.mkdir(parents=True, exist_ok=True)
            
            # Move file/directory
            shutil.move(str(source), str(destination))
            
            return {
                "moved_from": str(source),
                "moved_to": str(destination)
            }
            
        except Exception as e:
            raise Exception(f"Failed to move: {str(e)}")
    
    def copy_file(self, source_path, destination_path):
        """Copy file or directory"""
        try:
            source = Path(source_path)
            destination = Path(destination_path)
            
            if not source.exists():
                raise Exception(f"Source does not exist: {source}")
            
            # Create destination directory if needed
            destination.parent.mkdir(parents=True, exist_ok=True)
            
            if source.is_file():
                shutil.copy2(str(source), str(destination))
            elif source.is_dir():
                shutil.copytree(str(source), str(destination))
            
            return {
                "copied_from": str(source),
                "copied_to": str(destination)
            }
            
        except Exception as e:
            raise Exception(f"Failed to copy: {str(e)}")
    
    def get_file_info(self, file_path):
        """Get detailed file information"""
        try:
            path = Path(file_path)
            
            if not path.exists():
                raise Exception(f"Path does not exist: {path}")
            
            stat_info = path.stat()
            
            info = {
                "name": path.name,
                "path": str(path),
                "type": "directory" if path.is_dir() else "file",
                "size": stat_info.st_size,
                "created": stat_info.st_ctime,
                "modified": stat_info.st_mtime,
                "accessed": stat_info.st_atime,
                "permissions": self._get_permissions(stat_info.st_mode)
            }
            
            if path.is_file():
                info["extension"] = path.suffix.lower()
                
                # Try to determine file type
                info["mime_type"] = self._get_mime_type(path)
            
            return info
            
        except Exception as e:
            raise Exception(f"Failed to get file info: {str(e)}")
    
    def _get_mime_type(self, path):
        """Get MIME type of file"""
        try:
            import mimetypes
            mime_type, _ = mimetypes.guess_type(str(path))
            return mime_type or "application/octet-stream"
        except:
            return "application/octet-stream"
    
    def search_files(self, directory, pattern, recursive=True):
        """Search for files matching pattern"""
        try:
            path = Path(directory)
            
            if not path.exists() or not path.is_dir():
                raise Exception(f"Invalid directory: {path}")
            
            matches = []
            
            if recursive:
                search_pattern = f"**/{pattern}"
            else:
                search_pattern = pattern
            
            for match in path.glob(search_pattern):
                try:
                    stat_info = match.stat()
                    
                    match_info = {
                        "name": match.name,
                        "path": str(match),
                        "type": "directory" if match.is_dir() else "file",
                        "size": stat_info.st_size if match.is_file() else 0,
                        "modified": stat_info.st_mtime
                    }
                    
                    matches.append(match_info)
                    
                except (PermissionError, OSError):
                    continue
            
            return {
                "pattern": pattern,
                "directory": str(path),
                "recursive": recursive,
                "matches": matches,
                "total_matches": len(matches)
            }
            
        except Exception as e:
            raise Exception(f"Failed to search files: {str(e)}")
