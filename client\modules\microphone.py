# -*- coding: utf-8 -*-
"""
CyberTrap For3on - Microphone Recording Module
Records audio from microphone
"""

import pyaudio
import wave
import base64
import threading
import time
import io

class MicrophoneRecorder:
    """Microphone recording functionality"""
    
    def __init__(self):
        self.is_recording = False
        self.audio_data = []
        self.sample_rate = 44100
        self.chunk_size = 1024
        self.channels = 1
        self.format = pyaudio.paInt16
        
    def record(self, duration=10):
        """Record audio for specified duration"""
        try:
            # Initialize PyAudio
            audio = pyaudio.PyAudio()
            
            # Open stream
            stream = audio.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size
            )
            
            print(f"Recording for {duration} seconds...")
            
            frames = []
            
            # Record audio
            for _ in range(0, int(self.sample_rate / self.chunk_size * duration)):
                data = stream.read(self.chunk_size)
                frames.append(data)
            
            # Stop and close stream
            stream.stop_stream()
            stream.close()
            audio.terminate()
            
            # Create WAV file in memory
            wav_buffer = io.BytesIO()
            
            with wave.open(wav_buffer, 'wb') as wav_file:
                wav_file.setnchannels(self.channels)
                wav_file.setsampwidth(audio.get_sample_size(self.format))
                wav_file.setframerate(self.sample_rate)
                wav_file.writeframes(b''.join(frames))
            
            # Get WAV data
            wav_data = wav_buffer.getvalue()
            
            # Convert to base64
            base64_data = base64.b64encode(wav_data).decode('utf-8')
            
            return {
                "audio_data": base64_data,
                "duration": duration,
                "sample_rate": self.sample_rate,
                "channels": self.channels,
                "format": "WAV",
                "size": len(wav_data),
                "timestamp": time.time()
            }
            
        except Exception as e:
            raise Exception(f"Microphone recording failed: {str(e)}")
    
    def start_continuous_recording(self):
        """Start continuous recording in background"""
        if self.is_recording:
            return False
        
        self.is_recording = True
        self.audio_data = []
        
        def record_thread():
            try:
                audio = pyaudio.PyAudio()
                
                stream = audio.open(
                    format=self.format,
                    channels=self.channels,
                    rate=self.sample_rate,
                    input=True,
                    frames_per_buffer=self.chunk_size
                )
                
                while self.is_recording:
                    data = stream.read(self.chunk_size)
                    self.audio_data.append(data)
                
                stream.stop_stream()
                stream.close()
                audio.terminate()
                
            except Exception as e:
                self.is_recording = False
        
        thread = threading.Thread(target=record_thread)
        thread.daemon = True
        thread.start()
        
        return True
    
    def stop_continuous_recording(self):
        """Stop continuous recording and return data"""
        if not self.is_recording:
            return None
        
        self.is_recording = False
        time.sleep(0.5)  # Wait for thread to finish
        
        try:
            # Create WAV file from recorded data
            wav_buffer = io.BytesIO()
            
            audio = pyaudio.PyAudio()
            
            with wave.open(wav_buffer, 'wb') as wav_file:
                wav_file.setnchannels(self.channels)
                wav_file.setsampwidth(audio.get_sample_size(self.format))
                wav_file.setframerate(self.sample_rate)
                wav_file.writeframes(b''.join(self.audio_data))
            
            audio.terminate()
            
            # Get WAV data
            wav_data = wav_buffer.getvalue()
            
            # Convert to base64
            base64_data = base64.b64encode(wav_data).decode('utf-8')
            
            # Calculate duration
            duration = len(self.audio_data) * self.chunk_size / self.sample_rate
            
            # Clear audio data
            self.audio_data = []
            
            return {
                "audio_data": base64_data,
                "duration": duration,
                "sample_rate": self.sample_rate,
                "channels": self.channels,
                "format": "WAV",
                "size": len(wav_data),
                "timestamp": time.time()
            }
            
        except Exception as e:
            raise Exception(f"Failed to process recorded audio: {str(e)}")
    
    def get_audio_devices(self):
        """Get list of available audio input devices"""
        try:
            audio = pyaudio.PyAudio()
            devices = []
            
            for i in range(audio.get_device_count()):
                device_info = audio.get_device_info_by_index(i)
                
                if device_info['maxInputChannels'] > 0:
                    devices.append({
                        "index": i,
                        "name": device_info['name'],
                        "channels": device_info['maxInputChannels'],
                        "sample_rate": device_info['defaultSampleRate']
                    })
            
            audio.terminate()
            return devices
            
        except Exception as e:
            return []
    
    def test_microphone(self):
        """Test microphone functionality"""
        try:
            # Record 1 second of audio
            test_recording = self.record(1)
            
            return {
                "microphone_working": True,
                "test_recording_size": test_recording["size"],
                "available_devices": self.get_audio_devices()
            }
            
        except Exception as e:
            return {
                "microphone_working": False,
                "error": str(e),
                "available_devices": self.get_audio_devices()
            }
