# -*- coding: utf-8 -*-
"""
CyberTrap For3on - System Information Collector
Collects comprehensive system information
"""

import os
import platform
import socket
import psutil
import subprocess
import ctypes
import requests
from pathlib import Path

class SystemInfoCollector:
    """Collects system information"""
    
    def __init__(self):
        self.info = {}
    
    def get_hostname(self):
        """Get computer hostname"""
        try:
            return socket.gethostname()
        except:
            return "Unknown"
    
    def get_username(self):
        """Get current username"""
        try:
            return os.getenv('USERNAME') or os.getenv('USER') or "Unknown"
        except:
            return "Unknown"
    
    def get_os_info(self):
        """Get operating system information"""
        try:
            system = platform.system()
            release = platform.release()
            version = platform.version()
            architecture = platform.architecture()[0]
            
            return f"{system} {release} {version} ({architecture})"
        except:
            return "Unknown OS"
    
    def get_local_ip(self):
        """Get local IP address"""
        try:
            # Connect to a remote server to get local IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except:
            return "Unknown"
    
    def get_public_ip(self):
        """Get public IP address"""
        try:
            response = requests.get("https://api.ipify.org", timeout=10)
            return response.text.strip()
        except:
            try:
                response = requests.get("https://httpbin.org/ip", timeout=10)
                return response.json().get("origin", "Unknown")
            except:
                return "Unknown"
    
    def is_admin(self):
        """Check if running with admin privileges"""
        try:
            if platform.system() == "Windows":
                return ctypes.windll.shell32.IsUserAnAdmin() != 0
            else:
                return os.geteuid() == 0
        except:
            return False
    
    def get_system_specs(self):
        """Get system specifications"""
        try:
            specs = {
                "cpu_count": psutil.cpu_count(),
                "cpu_percent": psutil.cpu_percent(interval=1),
                "memory_total": psutil.virtual_memory().total,
                "memory_available": psutil.virtual_memory().available,
                "disk_usage": {}
            }
            
            # Get disk usage for all drives
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    specs["disk_usage"][partition.device] = {
                        "total": usage.total,
                        "used": usage.used,
                        "free": usage.free
                    }
                except:
                    continue
            
            return specs
        except:
            return {}
    
    def get_network_info(self):
        """Get network interface information"""
        try:
            interfaces = {}
            for interface, addrs in psutil.net_if_addrs().items():
                interface_info = []
                for addr in addrs:
                    if addr.family == socket.AF_INET:  # IPv4
                        interface_info.append({
                            "ip": addr.address,
                            "netmask": addr.netmask,
                            "broadcast": addr.broadcast
                        })
                if interface_info:
                    interfaces[interface] = interface_info
            return interfaces
        except:
            return {}
    
    def get_installed_software(self):
        """Get list of installed software (Windows)"""
        try:
            if platform.system() != "Windows":
                return []
            
            software_list = []
            
            # Query registry for installed programs
            import winreg
            
            registry_paths = [
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
                r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
            ]
            
            for registry_path in registry_paths:
                try:
                    registry_key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, registry_path)
                    
                    for i in range(winreg.QueryInfoKey(registry_key)[0]):
                        try:
                            subkey_name = winreg.EnumKey(registry_key, i)
                            subkey = winreg.OpenKey(registry_key, subkey_name)
                            
                            try:
                                display_name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                                try:
                                    display_version = winreg.QueryValueEx(subkey, "DisplayVersion")[0]
                                except:
                                    display_version = "Unknown"
                                
                                software_list.append({
                                    "name": display_name,
                                    "version": display_version
                                })
                            except:
                                pass
                            
                            winreg.CloseKey(subkey)
                        except:
                            continue
                    
                    winreg.CloseKey(registry_key)
                except:
                    continue
            
            return software_list[:50]  # Limit to first 50 programs
        except:
            return []
    
    def get_running_processes(self):
        """Get list of running processes"""
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'username', 'memory_percent']):
                try:
                    processes.append({
                        "pid": proc.info['pid'],
                        "name": proc.info['name'],
                        "username": proc.info['username'],
                        "memory_percent": proc.info['memory_percent']
                    })
                except:
                    continue
            
            # Sort by memory usage and return top 20
            processes.sort(key=lambda x: x['memory_percent'] or 0, reverse=True)
            return processes[:20]
        except:
            return []
    
    def get_startup_programs(self):
        """Get startup programs (Windows)"""
        try:
            if platform.system() != "Windows":
                return []
            
            startup_programs = []
            
            # Check registry startup locations
            import winreg
            
            startup_keys = [
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run"),
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce")
            ]
            
            for hkey, subkey_path in startup_keys:
                try:
                    registry_key = winreg.OpenKey(hkey, subkey_path)
                    
                    for i in range(winreg.QueryInfoKey(registry_key)[1]):
                        try:
                            name, value, _ = winreg.EnumValue(registry_key, i)
                            startup_programs.append({
                                "name": name,
                                "path": value,
                                "location": subkey_path
                            })
                        except:
                            continue
                    
                    winreg.CloseKey(registry_key)
                except:
                    continue
            
            return startup_programs
        except:
            return []
    
    def collect_all(self):
        """Collect all system information"""
        self.info = {
            "hostname": self.get_hostname(),
            "username": self.get_username(),
            "os_info": self.get_os_info(),
            "local_ip": self.get_local_ip(),
            "public_ip": self.get_public_ip(),
            "is_admin": self.is_admin(),
            "system_specs": self.get_system_specs(),
            "network_info": self.get_network_info(),
            "installed_software": self.get_installed_software(),
            "running_processes": self.get_running_processes(),
            "startup_programs": self.get_startup_programs()
        }
        
        return self.info
