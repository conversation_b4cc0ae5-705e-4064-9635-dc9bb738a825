# -*- coding: utf-8 -*-
"""
CyberTrap For3on - Webcam Capture Module
Captures images from webcam
"""

import cv2
import base64
import time
import numpy as np

class WebcamCapture:
    """Webcam capture functionality"""
    
    def __init__(self):
        self.camera = None
        self.is_capturing = False
    
    def capture(self, camera_index=0, quality=85):
        """Capture single image from webcam"""
        try:
            # Initialize camera
            cap = cv2.VideoCapture(camera_index)
            
            if not cap.isOpened():
                raise Exception(f"Cannot open camera {camera_index}")
            
            # Set camera properties for better quality
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
            cap.set(cv2.CAP_PROP_FPS, 30)
            
            # Capture frame
            ret, frame = cap.read()
            
            if not ret:
                cap.release()
                raise Exception("Failed to capture frame")
            
            # Release camera
            cap.release()
            
            # Encode image as JPEG
            encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), quality]
            _, buffer = cv2.imencode('.jpg', frame, encode_param)
            
            # Convert to base64
            image_data = buffer.tobytes()
            base64_data = base64.b64encode(image_data).decode('utf-8')
            
            return {
                "image_data": base64_data,
                "width": frame.shape[1],
                "height": frame.shape[0],
                "channels": frame.shape[2],
                "format": "JPEG",
                "size": len(image_data),
                "timestamp": time.time(),
                "camera_index": camera_index
            }
            
        except Exception as e:
            raise Exception(f"Webcam capture failed: {str(e)}")
    
    def capture_multiple_cameras(self, max_cameras=5, quality=85):
        """Capture from multiple cameras if available"""
        captures = []
        
        for camera_index in range(max_cameras):
            try:
                capture_data = self.capture(camera_index, quality)
                captures.append(capture_data)
            except:
                # Camera not available or failed
                continue
        
        return {
            "total_cameras": len(captures),
            "captures": captures
        }
    
    def get_available_cameras(self):
        """Get list of available cameras"""
        available_cameras = []
        
        for camera_index in range(10):  # Check first 10 camera indices
            try:
                cap = cv2.VideoCapture(camera_index)
                
                if cap.isOpened():
                    # Get camera properties
                    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                    fps = int(cap.get(cv2.CAP_PROP_FPS))
                    
                    available_cameras.append({
                        "index": camera_index,
                        "width": width,
                        "height": height,
                        "fps": fps
                    })
                
                cap.release()
                
            except:
                continue
        
        return available_cameras
    
    def start_video_stream(self, camera_index=0, duration=10):
        """Capture video stream for specified duration"""
        try:
            cap = cv2.VideoCapture(camera_index)
            
            if not cap.isOpened():
                raise Exception(f"Cannot open camera {camera_index}")
            
            # Set properties
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            cap.set(cv2.CAP_PROP_FPS, 15)
            
            frames = []
            start_time = time.time()
            
            while (time.time() - start_time) < duration:
                ret, frame = cap.read()
                
                if not ret:
                    break
                
                # Encode frame as JPEG
                encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), 70]
                _, buffer = cv2.imencode('.jpg', frame, encode_param)
                
                # Convert to base64
                frame_data = base64.b64encode(buffer.tobytes()).decode('utf-8')
                
                frames.append({
                    "frame_data": frame_data,
                    "timestamp": time.time()
                })
                
                # Limit frames to prevent memory issues
                if len(frames) > 150:  # ~10 seconds at 15 FPS
                    break
            
            cap.release()
            
            return {
                "total_frames": len(frames),
                "duration": time.time() - start_time,
                "frames": frames,
                "camera_index": camera_index
            }
            
        except Exception as e:
            raise Exception(f"Video stream capture failed: {str(e)}")
    
    def capture_with_motion_detection(self, camera_index=0, sensitivity=30, duration=60):
        """Capture images when motion is detected"""
        try:
            cap = cv2.VideoCapture(camera_index)
            
            if not cap.isOpened():
                raise Exception(f"Cannot open camera {camera_index}")
            
            # Initialize background subtractor
            backSub = cv2.createBackgroundSubtractorMOG2()
            
            motion_captures = []
            start_time = time.time()
            
            while (time.time() - start_time) < duration:
                ret, frame = cap.read()
                
                if not ret:
                    break
                
                # Apply background subtraction
                fgMask = backSub.apply(frame)
                
                # Count non-zero pixels (motion)
                motion_pixels = cv2.countNonZero(fgMask)
                
                # If motion detected
                if motion_pixels > sensitivity * 100:
                    # Encode frame as JPEG
                    encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), 85]
                    _, buffer = cv2.imencode('.jpg', frame, encode_param)
                    
                    # Convert to base64
                    image_data = buffer.tobytes()
                    base64_data = base64.b64encode(image_data).decode('utf-8')
                    
                    motion_captures.append({
                        "image_data": base64_data,
                        "motion_pixels": motion_pixels,
                        "timestamp": time.time(),
                        "size": len(image_data)
                    })
                    
                    # Limit captures
                    if len(motion_captures) > 50:
                        break
                
                time.sleep(0.1)  # Small delay
            
            cap.release()
            
            return {
                "total_captures": len(motion_captures),
                "duration": time.time() - start_time,
                "captures": motion_captures,
                "camera_index": camera_index
            }
            
        except Exception as e:
            raise Exception(f"Motion detection capture failed: {str(e)}")
    
    def test_camera(self, camera_index=0):
        """Test camera functionality"""
        try:
            # Try to capture a test image
            test_capture = self.capture(camera_index)
            
            return {
                "camera_working": True,
                "camera_index": camera_index,
                "resolution": f"{test_capture['width']}x{test_capture['height']}",
                "test_image_size": test_capture["size"]
            }
            
        except Exception as e:
            return {
                "camera_working": False,
                "camera_index": camera_index,
                "error": str(e)
            }
