# -*- coding: utf-8 -*-
"""
CyberTrap For3on - WiFi Password Extractor
Extracts saved WiFi passwords from Windows
"""

import subprocess
import re
import xml.etree.ElementTree as ET
from pathlib import Path

class WiFiPasswordExtractor:
    """Extract WiFi passwords from Windows"""
    
    def __init__(self):
        self.wifi_profiles = []
    
    def get_wifi_profiles(self):
        """Get list of WiFi profiles"""
        try:
            # Run netsh command to get profiles
            result = subprocess.run(
                ["netsh", "wlan", "show", "profiles"],
                capture_output=True,
                text=True,
                shell=True
            )
            
            if result.returncode != 0:
                return []
            
            profiles = []
            
            # Parse output to extract profile names
            for line in result.stdout.split('\n'):
                if "All User Profile" in line:
                    # Extract profile name
                    match = re.search(r':\s*(.+)', line)
                    if match:
                        profile_name = match.group(1).strip()
                        profiles.append(profile_name)
            
            return profiles
            
        except Exception as e:
            return []
    
    def get_wifi_password(self, profile_name):
        """Get password for specific WiFi profile"""
        try:
            # Run netsh command to get profile details
            result = subprocess.run(
                ["netsh", "wlan", "show", "profile", profile_name, "key=clear"],
                capture_output=True,
                text=True,
                shell=True
            )
            
            if result.returncode != 0:
                return None
            
            # Parse output to extract password
            for line in result.stdout.split('\n'):
                if "Key Content" in line:
                    match = re.search(r':\s*(.+)', line)
                    if match:
                        return match.group(1).strip()
            
            return None
            
        except Exception as e:
            return None
    
    def get_wifi_details(self, profile_name):
        """Get detailed information about WiFi profile"""
        try:
            # Run netsh command to get profile details
            result = subprocess.run(
                ["netsh", "wlan", "show", "profile", profile_name, "key=clear"],
                capture_output=True,
                text=True,
                shell=True
            )
            
            if result.returncode != 0:
                return None
            
            details = {
                "profile_name": profile_name,
                "ssid": "",
                "authentication": "",
                "cipher": "",
                "security_key": "",
                "password": "",
                "connection_mode": "",
                "network_type": ""
            }
            
            # Parse output
            for line in result.stdout.split('\n'):
                line = line.strip()
                
                if "SSID name" in line:
                    match = re.search(r':\s*"?([^"]+)"?', line)
                    if match:
                        details["ssid"] = match.group(1).strip()
                
                elif "Authentication" in line:
                    match = re.search(r':\s*(.+)', line)
                    if match:
                        details["authentication"] = match.group(1).strip()
                
                elif "Cipher" in line:
                    match = re.search(r':\s*(.+)', line)
                    if match:
                        details["cipher"] = match.group(1).strip()
                
                elif "Security key" in line:
                    match = re.search(r':\s*(.+)', line)
                    if match:
                        details["security_key"] = match.group(1).strip()
                
                elif "Key Content" in line:
                    match = re.search(r':\s*(.+)', line)
                    if match:
                        details["password"] = match.group(1).strip()
                
                elif "Connection mode" in line:
                    match = re.search(r':\s*(.+)', line)
                    if match:
                        details["connection_mode"] = match.group(1).strip()
                
                elif "Network type" in line:
                    match = re.search(r':\s*(.+)', line)
                    if match:
                        details["network_type"] = match.group(1).strip()
            
            return details
            
        except Exception as e:
            return None
    
    def extract_all(self):
        """Extract all WiFi passwords"""
        wifi_data = {
            "total_profiles": 0,
            "profiles_with_passwords": 0,
            "wifi_networks": []
        }
        
        try:
            # Get all WiFi profiles
            profiles = self.get_wifi_profiles()
            wifi_data["total_profiles"] = len(profiles)
            
            for profile in profiles:
                details = self.get_wifi_details(profile)
                
                if details:
                    wifi_data["wifi_networks"].append(details)
                    
                    if details["password"]:
                        wifi_data["profiles_with_passwords"] += 1
            
            return wifi_data
            
        except Exception as e:
            return {
                "error": f"Failed to extract WiFi passwords: {str(e)}",
                "total_profiles": 0,
                "profiles_with_passwords": 0,
                "wifi_networks": []
            }
    
    def get_current_wifi_info(self):
        """Get information about currently connected WiFi"""
        try:
            # Get current WiFi connection
            result = subprocess.run(
                ["netsh", "wlan", "show", "interfaces"],
                capture_output=True,
                text=True,
                shell=True
            )
            
            if result.returncode != 0:
                return None
            
            current_wifi = {
                "profile": "",
                "ssid": "",
                "bssid": "",
                "network_type": "",
                "radio_type": "",
                "authentication": "",
                "cipher": "",
                "connection_mode": "",
                "channel": "",
                "receive_rate": "",
                "transmit_rate": "",
                "signal": ""
            }
            
            # Parse output
            for line in result.stdout.split('\n'):
                line = line.strip()
                
                if "Profile" in line and ":" in line:
                    match = re.search(r':\s*(.+)', line)
                    if match:
                        current_wifi["profile"] = match.group(1).strip()
                
                elif "SSID" in line and ":" in line:
                    match = re.search(r':\s*(.+)', line)
                    if match:
                        current_wifi["ssid"] = match.group(1).strip()
                
                elif "BSSID" in line:
                    match = re.search(r':\s*(.+)', line)
                    if match:
                        current_wifi["bssid"] = match.group(1).strip()
                
                elif "Network type" in line:
                    match = re.search(r':\s*(.+)', line)
                    if match:
                        current_wifi["network_type"] = match.group(1).strip()
                
                elif "Radio type" in line:
                    match = re.search(r':\s*(.+)', line)
                    if match:
                        current_wifi["radio_type"] = match.group(1).strip()
                
                elif "Authentication" in line:
                    match = re.search(r':\s*(.+)', line)
                    if match:
                        current_wifi["authentication"] = match.group(1).strip()
                
                elif "Cipher" in line:
                    match = re.search(r':\s*(.+)', line)
                    if match:
                        current_wifi["cipher"] = match.group(1).strip()
                
                elif "Channel" in line:
                    match = re.search(r':\s*(.+)', line)
                    if match:
                        current_wifi["channel"] = match.group(1).strip()
                
                elif "Receive rate" in line:
                    match = re.search(r':\s*(.+)', line)
                    if match:
                        current_wifi["receive_rate"] = match.group(1).strip()
                
                elif "Transmit rate" in line:
                    match = re.search(r':\s*(.+)', line)
                    if match:
                        current_wifi["transmit_rate"] = match.group(1).strip()
                
                elif "Signal" in line:
                    match = re.search(r':\s*(.+)', line)
                    if match:
                        current_wifi["signal"] = match.group(1).strip()
            
            # Get password for current profile
            if current_wifi["profile"]:
                password = self.get_wifi_password(current_wifi["profile"])
                current_wifi["password"] = password or ""
            
            return current_wifi
            
        except Exception as e:
            return None
    
    def scan_available_networks(self):
        """Scan for available WiFi networks"""
        try:
            # Refresh available networks
            subprocess.run(
                ["netsh", "wlan", "refresh"],
                capture_output=True,
                text=True,
                shell=True
            )
            
            # Get available networks
            result = subprocess.run(
                ["netsh", "wlan", "show", "profiles"],
                capture_output=True,
                text=True,
                shell=True
            )
            
            if result.returncode != 0:
                return []
            
            # This is a simplified version - full implementation would parse
            # the detailed network scan results
            return self.get_wifi_profiles()
            
        except Exception as e:
            return []
