#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CyberTrap For3on - Development Tools
Utilities for development and testing
"""

import os
import sys
import shutil
import subprocess
import json
from pathlib import Path
from datetime import datetime

class DevTools:
    """Development tools for CyberTrap For3on"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.output_dir = self.base_dir / "output"
        self.logs_dir = self.base_dir / "logs"
        
    def clean_project(self):
        """Clean project files"""
        print("🧹 Cleaning project files...")
        
        # Directories to clean
        clean_dirs = [
            "__pycache__",
            "*.pyc",
            "build",
            "dist",
            "*.egg-info"
        ]
        
        # Clean Python cache files
        for root, dirs, files in os.walk(self.base_dir):
            # Remove __pycache__ directories
            if "__pycache__" in dirs:
                cache_dir = Path(root) / "__pycache__"
                print(f"   Removing: {cache_dir}")
                shutil.rmtree(cache_dir, ignore_errors=True)
            
            # Remove .pyc files
            for file in files:
                if file.endswith('.pyc'):
                    pyc_file = Path(root) / file
                    print(f"   Removing: {pyc_file}")
                    pyc_file.unlink(missing_ok=True)
        
        # Clean build directories
        build_dirs = ["build", "dist"]
        for build_dir in build_dirs:
            build_path = self.base_dir / build_dir
            if build_path.exists():
                print(f"   Removing: {build_path}")
                shutil.rmtree(build_path, ignore_errors=True)
        
        print("✅ Project cleaned")
    
    def check_dependencies(self):
        """Check if all dependencies are installed"""
        print("📦 Checking dependencies...")
        
        requirements_file = self.base_dir / "requirements.txt"
        if not requirements_file.exists():
            print("❌ requirements.txt not found")
            return False
        
        try:
            with open(requirements_file, 'r') as f:
                requirements = f.read().splitlines()
            
            missing_packages = []
            
            for requirement in requirements:
                if requirement.strip() and not requirement.startswith('#'):
                    package_name = requirement.split('==')[0].split('>=')[0].split('<=')[0]
                    
                    try:
                        __import__(package_name.replace('-', '_'))
                        print(f"   ✅ {package_name}")
                    except ImportError:
                        print(f"   ❌ {package_name} - Missing")
                        missing_packages.append(requirement)
            
            if missing_packages:
                print(f"\n📋 Missing packages ({len(missing_packages)}):")
                for package in missing_packages:
                    print(f"   - {package}")
                print("\n💡 Install with: pip install -r requirements.txt")
                return False
            else:
                print("✅ All dependencies are installed")
                return True
                
        except Exception as e:
            print(f"❌ Error checking dependencies: {e}")
            return False
    
    def run_tests(self):
        """Run basic tests"""
        print("🧪 Running tests...")
        
        try:
            # Test imports
            sys.path.append(str(self.base_dir))
            
            print("   Testing shared modules...")
            from shared.encryption import crypto_manager
            from shared.protocol import Protocol, MessageType
            from shared.config import Config
            
            print("   Testing encryption...")
            crypto_manager.generate_rsa_keys()
            crypto_manager.generate_aes_key()
            
            test_data = "Hello, CyberTrap!"
            encrypted = crypto_manager.encrypt_data(test_data)
            decrypted = crypto_manager.decrypt_data(encrypted)
            
            if decrypted == test_data:
                print("   ✅ Encryption test passed")
            else:
                print("   ❌ Encryption test failed")
                return False
            
            print("   Testing protocol...")
            message = Protocol.pack_message(MessageType.HEARTBEAT, {"status": "test"})
            if message:
                print("   ✅ Protocol test passed")
            else:
                print("   ❌ Protocol test failed")
                return False
            
            print("✅ All tests passed")
            return True
            
        except Exception as e:
            print(f"❌ Tests failed: {e}")
            return False
    
    def generate_docs(self):
        """Generate documentation"""
        print("📚 Generating documentation...")
        
        docs_dir = self.base_dir / "docs"
        docs_dir.mkdir(exist_ok=True)
        
        # Generate module documentation
        modules = [
            "shared/config.py",
            "shared/encryption.py", 
            "shared/protocol.py",
            "server/server_core.py",
            "client/client.py"
        ]
        
        for module in modules:
            module_path = self.base_dir / module
            if module_path.exists():
                print(f"   Documenting: {module}")
                # Here you could use pydoc or sphinx to generate docs
        
        print("✅ Documentation generated")
    
    def create_release(self, version="1.0.0"):
        """Create release package"""
        print(f"📦 Creating release package v{version}...")
        
        release_dir = self.base_dir / f"release_v{version}"
        if release_dir.exists():
            shutil.rmtree(release_dir)
        
        release_dir.mkdir()
        
        # Copy essential files
        essential_files = [
            "README.md",
            "LICENSE", 
            "INSTALL.md",
            "requirements.txt",
            "config_example.json",
            "run_server.py",
            "run_client.py",
            "setup.py",
            "start_cybertrap.bat"
        ]
        
        for file in essential_files:
            src = self.base_dir / file
            if src.exists():
                shutil.copy2(src, release_dir / file)
                print(f"   Copied: {file}")
        
        # Copy directories
        essential_dirs = ["server", "client", "shared", "builder"]
        
        for directory in essential_dirs:
            src_dir = self.base_dir / directory
            if src_dir.exists():
                dst_dir = release_dir / directory
                shutil.copytree(src_dir, dst_dir, ignore=shutil.ignore_patterns('__pycache__', '*.pyc'))
                print(f"   Copied: {directory}/")
        
        # Create release info
        release_info = {
            "version": version,
            "release_date": datetime.now().isoformat(),
            "description": "CyberTrap For3on - Advanced RAT for Cybersecurity Defense",
            "author": "For3on Security Team",
            "files_included": essential_files + essential_dirs
        }
        
        with open(release_dir / "release_info.json", 'w') as f:
            json.dump(release_info, f, indent=4)
        
        print(f"✅ Release package created: {release_dir}")
        return release_dir
    
    def build_executable(self):
        """Build server executable"""
        print("🔨 Building server executable...")
        
        try:
            # Check if pyinstaller is available
            subprocess.run(["pyinstaller", "--version"], check=True, capture_output=True)
            
            server_file = self.base_dir / "server" / "main.py"
            
            cmd = [
                "pyinstaller",
                "--onefile",
                "--windowed",
                "--name", "CyberTrap_Server",
                "--distpath", str(self.output_dir),
                "--workpath", str(self.base_dir / "build"),
                "--specpath", str(self.base_dir),
                str(server_file)
            ]
            
            print("   Running PyInstaller...")
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                exe_path = self.output_dir / "CyberTrap_Server.exe"
                print(f"✅ Executable built: {exe_path}")
                return exe_path
            else:
                print(f"❌ Build failed: {result.stderr}")
                return None
                
        except subprocess.CalledProcessError:
            print("❌ PyInstaller not found. Install with: pip install pyinstaller")
            return None
        except Exception as e:
            print(f"❌ Build failed: {e}")
            return None
    
    def show_project_stats(self):
        """Show project statistics"""
        print("📊 Project Statistics")
        print("=" * 30)
        
        # Count files by type
        file_counts = {}
        total_lines = 0
        
        for root, dirs, files in os.walk(self.base_dir):
            # Skip certain directories
            dirs[:] = [d for d in dirs if d not in ['__pycache__', '.git', 'build', 'dist']]
            
            for file in files:
                file_path = Path(root) / file
                ext = file_path.suffix.lower()
                
                if ext in ['.py', '.md', '.txt', '.json', '.bat']:
                    file_counts[ext] = file_counts.get(ext, 0) + 1
                    
                    if ext == '.py':
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                lines = len(f.readlines())
                                total_lines += lines
                        except:
                            pass
        
        print(f"Python files: {file_counts.get('.py', 0)}")
        print(f"Total Python lines: {total_lines:,}")
        print(f"Documentation files: {file_counts.get('.md', 0)}")
        print(f"Config files: {file_counts.get('.json', 0)}")
        print(f"Text files: {file_counts.get('.txt', 0)}")
        print(f"Batch files: {file_counts.get('.bat', 0)}")
        
        # Check project size
        total_size = 0
        for root, dirs, files in os.walk(self.base_dir):
            for file in files:
                file_path = Path(root) / file
                try:
                    total_size += file_path.stat().st_size
                except:
                    pass
        
        print(f"Project size: {total_size / 1024 / 1024:.2f} MB")

def main():
    """Main function"""
    if len(sys.argv) < 2:
        print("🔧 CyberTrap For3on - Development Tools")
        print("=" * 40)
        print("Usage: python dev_tools.py <command>")
        print()
        print("Commands:")
        print("  clean      - Clean project files")
        print("  deps       - Check dependencies")
        print("  test       - Run tests")
        print("  docs       - Generate documentation")
        print("  release    - Create release package")
        print("  build      - Build server executable")
        print("  stats      - Show project statistics")
        print("  all        - Run all checks")
        return
    
    command = sys.argv[1].lower()
    dev_tools = DevTools()
    
    if command == "clean":
        dev_tools.clean_project()
    elif command == "deps":
        dev_tools.check_dependencies()
    elif command == "test":
        dev_tools.run_tests()
    elif command == "docs":
        dev_tools.generate_docs()
    elif command == "release":
        version = sys.argv[2] if len(sys.argv) > 2 else "1.0.0"
        dev_tools.create_release(version)
    elif command == "build":
        dev_tools.build_executable()
    elif command == "stats":
        dev_tools.show_project_stats()
    elif command == "all":
        print("🔧 Running all development checks...")
        print()
        dev_tools.clean_project()
        print()
        dev_tools.check_dependencies()
        print()
        dev_tools.run_tests()
        print()
        dev_tools.show_project_stats()
        print()
        print("✅ All checks completed")
    else:
        print(f"❌ Unknown command: {command}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        sys.exit(1)
