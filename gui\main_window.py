# -*- coding: utf-8 -*-
"""
For3on CyberTrap - Professional RAT GUI
Advanced Dark Theme Interface with Animations
"""

import sys
import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from datetime import datetime
import json

class AnimatedButton(QPushButton):
    """Custom animated button with hover effects"""
    
    def __init__(self, text="", icon_path="", parent=None):
        super().__init__(text, parent)
        self.icon_path = icon_path
        self.animation = QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(200)
        self.animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # Glow effect
        self.glow_effect = QGraphicsDropShadowEffect()
        self.glow_effect.setBlurRadius(15)
        self.glow_effect.setColor(QColor(0, 255, 255, 100))
        self.glow_effect.setOffset(0, 0)
        
        self.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2d3748, stop:1 #1a202c);
                border: 2px solid #4a5568;
                border-radius: 8px;
                color: #e2e8f0;
                font-weight: bold;
                font-size: 12px;
                padding: 8px 16px;
                min-height: 30px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4a5568, stop:1 #2d3748);
                border: 2px solid #00ffff;
                color: #00ffff;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1a202c, stop:1 #2d3748);
            }
        """)
    
    def enterEvent(self, event):
        self.setGraphicsEffect(self.glow_effect)
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        self.setGraphicsEffect(None)
        super().leaveEvent(event)

class PulsingIcon(QLabel):
    """Animated pulsing icon"""
    
    def __init__(self, icon_path, size=32, parent=None):
        super().__init__(parent)
        self.setFixedSize(size, size)
        
        # Load and set icon
        pixmap = QPixmap(icon_path) if os.path.exists(icon_path) else self.create_default_icon(size)
        self.setPixmap(pixmap.scaled(size, size, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        self.setAlignment(Qt.AlignCenter)
        
        # Pulsing animation
        self.opacity_effect = QGraphicsOpacityEffect()
        self.setGraphicsEffect(self.opacity_effect)
        
        self.animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.animation.setDuration(1500)
        self.animation.setStartValue(0.3)
        self.animation.setEndValue(1.0)
        self.animation.setEasingCurve(QEasingCurve.InOutSine)
        self.animation.setLoopCount(-1)
        self.animation.start()
    
    def create_default_icon(self, size):
        """Create default icon if file not found"""
        pixmap = QPixmap(size, size)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.setBrush(QBrush(QColor(0, 255, 255)))
        painter.setPen(QPen(QColor(0, 200, 200), 2))
        painter.drawEllipse(2, 2, size-4, size-4)
        painter.end()
        
        return pixmap

class VictimCard(QFrame):
    """Custom card widget for displaying victim information"""
    
    clicked = pyqtSignal(dict)
    
    def __init__(self, victim_data, parent=None):
        super().__init__(parent)
        self.victim_data = victim_data
        self.setup_ui()
        
    def setup_ui(self):
        self.setFixedHeight(120)
        self.setStyleSheet("""
            VictimCard {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2d3748, stop:1 #1a202c);
                border: 2px solid #4a5568;
                border-radius: 12px;
                margin: 5px;
            }
            VictimCard:hover {
                border: 2px solid #00ffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #4a5568, stop:1 #2d3748);
            }
        """)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 10, 15, 10)
        
        # Status indicator
        status_icon = PulsingIcon("", 16)
        layout.addWidget(status_icon)
        
        # Victim info
        info_layout = QVBoxLayout()
        
        # Computer name and IP
        name_label = QLabel(f"🖥️ {self.victim_data.get('computer_name', 'Unknown')}")
        name_label.setStyleSheet("color: #00ffff; font-weight: bold; font-size: 14px;")
        info_layout.addWidget(name_label)
        
        ip_label = QLabel(f"🌐 {self.victim_data.get('ip', 'Unknown')}")
        ip_label.setStyleSheet("color: #e2e8f0; font-size: 12px;")
        info_layout.addWidget(ip_label)
        
        # OS and User
        os_user = f"👤 {self.victim_data.get('username', 'Unknown')} | {self.victim_data.get('os', 'Unknown')}"
        os_label = QLabel(os_user)
        os_label.setStyleSheet("color: #a0aec0; font-size: 11px;")
        info_layout.addWidget(os_label)
        
        # Connection time
        conn_time = self.victim_data.get('connected_time', datetime.now().strftime('%H:%M:%S'))
        time_label = QLabel(f"⏰ Connected: {conn_time}")
        time_label.setStyleSheet("color: #68d391; font-size: 10px;")
        info_layout.addWidget(time_label)
        
        layout.addLayout(info_layout)
        layout.addStretch()
        
        # Admin indicator
        if self.victim_data.get('is_admin', False):
            admin_label = QLabel("👑\nADMIN")
            admin_label.setAlignment(Qt.AlignCenter)
            admin_label.setStyleSheet("""
                color: #ffd700;
                font-weight: bold;
                font-size: 10px;
                background: rgba(255, 215, 0, 0.2);
                border-radius: 8px;
                padding: 5px;
            """)
            layout.addWidget(admin_label)
    
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.clicked.emit(self.victim_data)
        super().mousePressEvent(event)

class ControlPanel(QWidget):
    """Advanced control panel for victim interaction"""
    
    def __init__(self, victim_data, parent=None):
        super().__init__(parent)
        self.victim_data = victim_data
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # Header with victim info
        header = self.create_header()
        layout.addWidget(header)
        
        # Control tabs
        tabs = QTabWidget()
        tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #4a5568;
                border-radius: 8px;
                background: #1a202c;
            }
            QTabBar::tab {
                background: #2d3748;
                color: #e2e8f0;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }
            QTabBar::tab:selected {
                background: #00ffff;
                color: #1a202c;
                font-weight: bold;
            }
            QTabBar::tab:hover {
                background: #4a5568;
            }
        """)
        
        # Add tabs
        tabs.addTab(self.create_command_tab(), "💻 Commands")
        tabs.addTab(self.create_screen_tab(), "📸 Screen")
        tabs.addTab(self.create_files_tab(), "📁 Files")
        tabs.addTab(self.create_passwords_tab(), "🔑 Passwords")
        tabs.addTab(self.create_system_tab(), "⚙️ System")
        
        layout.addWidget(tabs)
        
        # Action buttons
        actions = self.create_action_buttons()
        layout.addWidget(actions)
    
    def create_header(self):
        header = QFrame()
        header.setFixedHeight(80)
        header.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1a202c, stop:1 #2d3748);
                border: 2px solid #00ffff;
                border-radius: 12px;
            }
        """)
        
        layout = QHBoxLayout(header)
        
        # Victim avatar
        avatar = QLabel("🎯")
        avatar.setStyleSheet("font-size: 32px; color: #00ffff;")
        layout.addWidget(avatar)
        
        # Victim details
        details = QVBoxLayout()
        
        name = QLabel(f"Target: {self.victim_data.get('computer_name', 'Unknown')}")
        name.setStyleSheet("color: #00ffff; font-weight: bold; font-size: 16px;")
        details.addWidget(name)
        
        info = QLabel(f"{self.victim_data.get('ip', 'Unknown')} | {self.victim_data.get('os', 'Unknown')}")
        info.setStyleSheet("color: #e2e8f0; font-size: 12px;")
        details.addWidget(info)
        
        layout.addLayout(details)
        layout.addStretch()
        
        # Status indicator
        status = QLabel("🟢 ACTIVE")
        status.setStyleSheet("color: #68d391; font-weight: bold; font-size: 14px;")
        layout.addWidget(status)
        
        return header
    
    def create_command_tab(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Command input
        cmd_layout = QHBoxLayout()
        
        self.cmd_input = QLineEdit()
        self.cmd_input.setPlaceholderText("Enter command (e.g., dir, whoami, systeminfo)...")
        self.cmd_input.setStyleSheet("""
            QLineEdit {
                background: #2d3748;
                border: 2px solid #4a5568;
                border-radius: 8px;
                color: #e2e8f0;
                font-family: 'Consolas', monospace;
                font-size: 12px;
                padding: 10px;
            }
            QLineEdit:focus {
                border: 2px solid #00ffff;
            }
        """)
        cmd_layout.addWidget(self.cmd_input)
        
        execute_btn = AnimatedButton("⚡ Execute")
        execute_btn.clicked.connect(self.execute_command)
        cmd_layout.addWidget(execute_btn)
        
        layout.addLayout(cmd_layout)
        
        # Command output
        self.cmd_output = QTextEdit()
        self.cmd_output.setStyleSheet("""
            QTextEdit {
                background: #1a202c;
                border: 2px solid #4a5568;
                border-radius: 8px;
                color: #00ff00;
                font-family: 'Consolas', monospace;
                font-size: 11px;
                padding: 10px;
            }
        """)
        self.cmd_output.setPlaceholderText("Command output will appear here...")
        layout.addWidget(self.cmd_output)
        
        # Quick commands
        quick_layout = QHBoxLayout()
        quick_commands = [
            ("📋 System Info", "systeminfo"),
            ("👤 Current User", "whoami"),
            ("🌐 Network Info", "ipconfig /all"),
            ("📂 List Files", "dir"),
            ("🔄 Processes", "tasklist")
        ]
        
        for name, cmd in quick_commands:
            btn = AnimatedButton(name)
            btn.clicked.connect(lambda checked, c=cmd: self.quick_command(c))
            quick_layout.addWidget(btn)
        
        layout.addLayout(quick_layout)
        
        return widget
    
    def create_screen_tab(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Screenshot controls
        controls = QHBoxLayout()
        
        screenshot_btn = AnimatedButton("📸 Take Screenshot")
        screenshot_btn.clicked.connect(self.take_screenshot)
        controls.addWidget(screenshot_btn)
        
        live_view_btn = AnimatedButton("📹 Live View")
        live_view_btn.clicked.connect(self.start_live_view)
        controls.addWidget(live_view_btn)
        
        save_btn = AnimatedButton("💾 Save Image")
        save_btn.clicked.connect(self.save_screenshot)
        controls.addWidget(save_btn)
        
        controls.addStretch()
        layout.addLayout(controls)
        
        # Screenshot display
        self.screenshot_label = QLabel()
        self.screenshot_label.setAlignment(Qt.AlignCenter)
        self.screenshot_label.setStyleSheet("""
            QLabel {
                background: #1a202c;
                border: 2px solid #4a5568;
                border-radius: 8px;
                color: #a0aec0;
                font-size: 14px;
            }
        """)
        self.screenshot_label.setText("📸\n\nClick 'Take Screenshot' to capture the victim's screen")
        
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.screenshot_label)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        return widget
    
    def create_files_tab(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # File browser controls
        controls = QHBoxLayout()
        
        self.path_input = QLineEdit("C:\\")
        self.path_input.setStyleSheet("""
            QLineEdit {
                background: #2d3748;
                border: 2px solid #4a5568;
                border-radius: 8px;
                color: #e2e8f0;
                padding: 8px;
            }
        """)
        controls.addWidget(QLabel("📂 Path:"))
        controls.addWidget(self.path_input)
        
        browse_btn = AnimatedButton("🔍 Browse")
        browse_btn.clicked.connect(self.browse_files)
        controls.addWidget(browse_btn)
        
        layout.addLayout(controls)
        
        # File list
        self.file_tree = QTreeWidget()
        self.file_tree.setHeaderLabels(["Name", "Type", "Size", "Modified"])
        self.file_tree.setStyleSheet("""
            QTreeWidget {
                background: #1a202c;
                border: 2px solid #4a5568;
                border-radius: 8px;
                color: #e2e8f0;
                font-size: 12px;
            }
            QTreeWidget::item {
                padding: 5px;
                border-bottom: 1px solid #2d3748;
            }
            QTreeWidget::item:selected {
                background: #00ffff;
                color: #1a202c;
            }
            QTreeWidget::item:hover {
                background: #4a5568;
            }
        """)
        layout.addWidget(self.file_tree)
        
        # File actions
        file_actions = QHBoxLayout()
        
        download_btn = AnimatedButton("⬇️ Download")
        download_btn.clicked.connect(self.download_file)
        file_actions.addWidget(download_btn)
        
        upload_btn = AnimatedButton("⬆️ Upload")
        upload_btn.clicked.connect(self.upload_file)
        file_actions.addWidget(upload_btn)
        
        delete_btn = AnimatedButton("🗑️ Delete")
        delete_btn.clicked.connect(self.delete_file)
        file_actions.addWidget(delete_btn)
        
        file_actions.addStretch()
        layout.addLayout(file_actions)
        
        return widget
    
    def create_passwords_tab(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Password extraction controls
        controls = QHBoxLayout()
        
        browser_btn = AnimatedButton("🌐 Browser Passwords")
        browser_btn.clicked.connect(self.extract_browser_passwords)
        controls.addWidget(browser_btn)
        
        wifi_btn = AnimatedButton("📶 WiFi Passwords")
        wifi_btn.clicked.connect(self.extract_wifi_passwords)
        controls.addWidget(wifi_btn)
        
        system_btn = AnimatedButton("🔐 System Passwords")
        system_btn.clicked.connect(self.extract_system_passwords)
        controls.addWidget(system_btn)
        
        controls.addStretch()
        layout.addLayout(controls)
        
        # Password display
        self.password_tree = QTreeWidget()
        self.password_tree.setHeaderLabels(["Source", "URL/Name", "Username", "Password"])
        self.password_tree.setStyleSheet("""
            QTreeWidget {
                background: #1a202c;
                border: 2px solid #4a5568;
                border-radius: 8px;
                color: #e2e8f0;
                font-size: 12px;
            }
            QTreeWidget::item {
                padding: 5px;
                border-bottom: 1px solid #2d3748;
            }
            QTreeWidget::item:selected {
                background: #00ffff;
                color: #1a202c;
            }
        """)
        layout.addWidget(self.password_tree)
        
        # Export button
        export_btn = AnimatedButton("📤 Export Passwords")
        export_btn.clicked.connect(self.export_passwords)
        layout.addWidget(export_btn)
        
        return widget
    
    def create_system_tab(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # System controls
        controls_grid = QGridLayout()
        
        # Row 1
        keylogger_btn = AnimatedButton("⌨️ Start Keylogger")
        keylogger_btn.clicked.connect(self.toggle_keylogger)
        controls_grid.addWidget(keylogger_btn, 0, 0)
        
        webcam_btn = AnimatedButton("📷 Webcam Capture")
        webcam_btn.clicked.connect(self.capture_webcam)
        controls_grid.addWidget(webcam_btn, 0, 1)
        
        # Row 2
        audio_btn = AnimatedButton("🎤 Record Audio")
        audio_btn.clicked.connect(self.record_audio)
        controls_grid.addWidget(audio_btn, 1, 0)
        
        shutdown_btn = AnimatedButton("⚡ Shutdown")
        shutdown_btn.clicked.connect(self.shutdown_system)
        controls_grid.addWidget(shutdown_btn, 1, 1)
        
        layout.addLayout(controls_grid)
        
        # System information display
        self.system_info = QTextEdit()
        self.system_info.setStyleSheet("""
            QTextEdit {
                background: #1a202c;
                border: 2px solid #4a5568;
                border-radius: 8px;
                color: #e2e8f0;
                font-family: 'Consolas', monospace;
                font-size: 11px;
                padding: 10px;
            }
        """)
        self.system_info.setPlaceholderText("System information and logs will appear here...")
        layout.addWidget(self.system_info)
        
        return widget
    
    def create_action_buttons(self):
        actions = QFrame()
        actions.setFixedHeight(60)
        actions.setStyleSheet("""
            QFrame {
                background: #2d3748;
                border-radius: 8px;
            }
        """)
        
        layout = QHBoxLayout(actions)
        
        # Disconnect button
        disconnect_btn = AnimatedButton("🔌 Disconnect")
        disconnect_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e53e3e, stop:1 #c53030);
                border: 2px solid #fc8181;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 8px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #fc8181, stop:1 #e53e3e);
            }
        """)
        disconnect_btn.clicked.connect(self.disconnect_victim)
        layout.addWidget(disconnect_btn)
        
        layout.addStretch()
        
        # Status label
        status_label = QLabel("🟢 Connection Active")
        status_label.setStyleSheet("color: #68d391; font-weight: bold; font-size: 14px;")
        layout.addWidget(status_label)
        
        return actions
    
    # Event handlers (placeholder implementations)
    def execute_command(self):
        command = self.cmd_input.text()
        if command:
            self.cmd_output.append(f"$ {command}")
            self.cmd_output.append("Command executed successfully!")
            self.cmd_input.clear()
    
    def quick_command(self, command):
        self.cmd_input.setText(command)
        self.execute_command()
    
    def take_screenshot(self):
        self.screenshot_label.setText("📸 Taking screenshot...")
        # Implement screenshot logic
    
    def start_live_view(self):
        # Implement live view logic
        pass
    
    def save_screenshot(self):
        # Implement save logic
        pass
    
    def browse_files(self):
        # Implement file browsing logic
        pass
    
    def download_file(self):
        # Implement download logic
        pass
    
    def upload_file(self):
        # Implement upload logic
        pass
    
    def delete_file(self):
        # Implement delete logic
        pass
    
    def extract_browser_passwords(self):
        # Implement browser password extraction
        pass
    
    def extract_wifi_passwords(self):
        # Implement WiFi password extraction
        pass
    
    def extract_system_passwords(self):
        # Implement system password extraction
        pass
    
    def export_passwords(self):
        # Implement password export
        pass
    
    def toggle_keylogger(self):
        # Implement keylogger toggle
        pass
    
    def capture_webcam(self):
        # Implement webcam capture
        pass
    
    def record_audio(self):
        # Implement audio recording
        pass
    
    def shutdown_system(self):
        # Implement system shutdown
        pass
    
    def disconnect_victim(self):
        # Implement disconnect logic
        self.parent().close()

class MainWindow(QMainWindow):
    """Main application window"""

    def __init__(self):
        super().__init__()
        self.victims = {}
        self.control_windows = {}
        self.setup_ui()
        self.apply_dark_theme()

    def setup_ui(self):
        self.setWindowTitle("For3on CyberTrap - Professional RAT Interface")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)

        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QHBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(15, 15, 15, 15)

        # Left panel - Victims list
        left_panel = self.create_victims_panel()
        main_layout.addWidget(left_panel, 1)

        # Right panel - Server info and controls
        right_panel = self.create_server_panel()
        main_layout.addWidget(right_panel, 1)

        # Setup menu bar
        self.create_menu_bar()

        # Setup status bar
        self.create_status_bar()

        # Center window
        self.center_window()

    def create_victims_panel(self):
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2d3748, stop:1 #1a202c);
                border: 2px solid #4a5568;
                border-radius: 12px;
            }
        """)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(20, 20, 20, 20)

        # Header
        header = QHBoxLayout()

        title = QLabel("🎯 Connected Victims")
        title.setStyleSheet("""
            color: #00ffff;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        """)
        header.addWidget(title)

        header.addStretch()

        # Victim count
        self.victim_count = QLabel("0")
        self.victim_count.setStyleSheet("""
            background: #00ffff;
            color: #1a202c;
            font-weight: bold;
            font-size: 14px;
            padding: 5px 10px;
            border-radius: 15px;
            min-width: 30px;
        """)
        self.victim_count.setAlignment(Qt.AlignCenter)
        header.addWidget(self.victim_count)

        layout.addLayout(header)

        # Search bar
        search_layout = QHBoxLayout()

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("🔍 Search victims...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                background: #1a202c;
                border: 2px solid #4a5568;
                border-radius: 8px;
                color: #e2e8f0;
                padding: 8px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border: 2px solid #00ffff;
            }
        """)
        search_layout.addWidget(self.search_input)

        refresh_btn = AnimatedButton("🔄")
        refresh_btn.setFixedSize(40, 40)
        refresh_btn.clicked.connect(self.refresh_victims)
        search_layout.addWidget(refresh_btn)

        layout.addLayout(search_layout)

        # Victims scroll area
        self.victims_scroll = QScrollArea()
        self.victims_scroll.setWidgetResizable(True)
        self.victims_scroll.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:vertical {
                background: #2d3748;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background: #4a5568;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: #00ffff;
            }
        """)

        self.victims_widget = QWidget()
        self.victims_layout = QVBoxLayout(self.victims_widget)
        self.victims_layout.setSpacing(10)
        self.victims_layout.addStretch()

        self.victims_scroll.setWidget(self.victims_widget)
        layout.addWidget(self.victims_scroll)

        # Add sample victims for demo
        self.add_sample_victims()

        return panel

    def create_server_panel(self):
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2d3748, stop:1 #1a202c);
                border: 2px solid #4a5568;
                border-radius: 12px;
            }
        """)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(20, 20, 20, 20)

        # Server status header
        status_header = self.create_server_status()
        layout.addWidget(status_header)

        # Server controls
        controls = self.create_server_controls()
        layout.addWidget(controls)

        # Server logs
        logs = self.create_server_logs()
        layout.addWidget(logs)

        # Statistics
        stats = self.create_statistics()
        layout.addWidget(stats)

        return panel

    def create_server_status(self):
        status = QFrame()
        status.setFixedHeight(100)
        status.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1a202c, stop:1 #2d3748);
                border: 2px solid #68d391;
                border-radius: 12px;
            }
        """)

        layout = QHBoxLayout(status)

        # Status icon
        status_icon = PulsingIcon("", 48)
        layout.addWidget(status_icon)

        # Status info
        info_layout = QVBoxLayout()

        status_label = QLabel("🟢 SERVER ONLINE")
        status_label.setStyleSheet("""
            color: #68d391;
            font-size: 16px;
            font-weight: bold;
        """)
        info_layout.addWidget(status_label)

        port_label = QLabel("Port: 4444 | Encryption: AES-256")
        port_label.setStyleSheet("color: #e2e8f0; font-size: 12px;")
        info_layout.addWidget(port_label)

        uptime_label = QLabel("Uptime: 02:34:56")
        uptime_label.setStyleSheet("color: #a0aec0; font-size: 11px;")
        info_layout.addWidget(uptime_label)

        layout.addLayout(info_layout)
        layout.addStretch()

        # Control buttons
        btn_layout = QVBoxLayout()

        start_btn = AnimatedButton("▶️ Start")
        start_btn.clicked.connect(self.start_server)
        btn_layout.addWidget(start_btn)

        stop_btn = AnimatedButton("⏹️ Stop")
        stop_btn.clicked.connect(self.stop_server)
        btn_layout.addWidget(stop_btn)

        layout.addLayout(btn_layout)

        return status

    def create_server_controls(self):
        controls = QGroupBox("🛠️ Server Controls")
        controls.setStyleSheet("""
            QGroupBox {
                color: #00ffff;
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #4a5568;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        layout = QGridLayout(controls)

        # Control buttons
        buttons = [
            ("🔧 Settings", self.open_settings),
            ("📊 Analytics", self.open_analytics),
            ("🔐 Security", self.open_security),
            ("📤 Export Data", self.export_data),
            ("🧹 Clear Logs", self.clear_logs),
            ("❓ Help", self.show_help)
        ]

        for i, (text, handler) in enumerate(buttons):
            btn = AnimatedButton(text)
            btn.clicked.connect(handler)
            layout.addWidget(btn, i // 2, i % 2)

        return controls

    def create_server_logs(self):
        logs_group = QGroupBox("📋 Server Logs")
        logs_group.setStyleSheet("""
            QGroupBox {
                color: #00ffff;
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #4a5568;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        layout = QVBoxLayout(logs_group)

        self.logs_text = QTextEdit()
        self.logs_text.setStyleSheet("""
            QTextEdit {
                background: #1a202c;
                border: 2px solid #4a5568;
                border-radius: 8px;
                color: #e2e8f0;
                font-family: 'Consolas', monospace;
                font-size: 10px;
                padding: 10px;
            }
        """)
        self.logs_text.setMaximumHeight(150)

        # Add sample logs
        sample_logs = [
            "[12:34:56] Server started on port 4444",
            "[12:35:02] New connection from *************",
            "[12:35:15] Client authenticated: DESKTOP-ABC123",
            "[12:36:30] Screenshot captured from DESKTOP-ABC123",
            "[12:37:45] Command executed on LAPTOP-XYZ789"
        ]

        for log in sample_logs:
            self.logs_text.append(f"<span style='color: #68d391;'>{log}</span>")

        layout.addWidget(self.logs_text)

        return logs_group

    def create_statistics(self):
        stats_group = QGroupBox("📈 Statistics")
        stats_group.setStyleSheet("""
            QGroupBox {
                color: #00ffff;
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #4a5568;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        layout = QGridLayout(stats_group)

        # Statistics items
        stats = [
            ("Total Connections", "127", "#68d391"),
            ("Active Sessions", "3", "#00ffff"),
            ("Data Transferred", "2.3 GB", "#ffd700"),
            ("Commands Executed", "1,456", "#ff6b6b")
        ]

        for i, (label, value, color) in enumerate(stats):
            stat_widget = QFrame()
            stat_widget.setStyleSheet(f"""
                QFrame {{
                    background: rgba(26, 32, 44, 0.8);
                    border: 1px solid {color};
                    border-radius: 8px;
                    padding: 10px;
                }}
            """)

            stat_layout = QVBoxLayout(stat_widget)

            value_label = QLabel(value)
            value_label.setStyleSheet(f"color: {color}; font-size: 18px; font-weight: bold;")
            value_label.setAlignment(Qt.AlignCenter)
            stat_layout.addWidget(value_label)

            label_widget = QLabel(label)
            label_widget.setStyleSheet("color: #a0aec0; font-size: 10px;")
            label_widget.setAlignment(Qt.AlignCenter)
            stat_layout.addWidget(label_widget)

            layout.addWidget(stat_widget, i // 2, i % 2)

        return stats_group

    def create_menu_bar(self):
        menubar = self.menuBar()
        menubar.setStyleSheet("""
            QMenuBar {
                background: #2d3748;
                color: #e2e8f0;
                border-bottom: 2px solid #4a5568;
                padding: 5px;
            }
            QMenuBar::item {
                background: transparent;
                padding: 8px 12px;
                border-radius: 4px;
            }
            QMenuBar::item:selected {
                background: #4a5568;
            }
            QMenu {
                background: #2d3748;
                color: #e2e8f0;
                border: 2px solid #4a5568;
                border-radius: 8px;
            }
            QMenu::item {
                padding: 8px 20px;
            }
            QMenu::item:selected {
                background: #00ffff;
                color: #1a202c;
            }
        """)

        # File menu
        file_menu = menubar.addMenu("📁 File")
        file_menu.addAction("🆕 New Session", self.new_session)
        file_menu.addAction("💾 Save Session", self.save_session)
        file_menu.addAction("📂 Load Session", self.load_session)
        file_menu.addSeparator()
        file_menu.addAction("🚪 Exit", self.close)

        # Tools menu
        tools_menu = menubar.addMenu("🛠️ Tools")
        tools_menu.addAction("🔨 Payload Builder", self.open_payload_builder)
        tools_menu.addAction("🔍 Network Scanner", self.open_network_scanner)
        tools_menu.addAction("📊 Traffic Monitor", self.open_traffic_monitor)

        # Help menu
        help_menu = menubar.addMenu("❓ Help")
        help_menu.addAction("📖 Documentation", self.show_documentation)
        help_menu.addAction("ℹ️ About", self.show_about)

    def create_status_bar(self):
        status = self.statusBar()
        status.setStyleSheet("""
            QStatusBar {
                background: #2d3748;
                color: #e2e8f0;
                border-top: 2px solid #4a5568;
                padding: 5px;
            }
        """)

        status.showMessage("🟢 For3on CyberTrap Ready - Server Online")

        # Add permanent widgets
        self.connection_label = QLabel("🔗 Connections: 0")
        self.connection_label.setStyleSheet("color: #00ffff; font-weight: bold;")
        status.addPermanentWidget(self.connection_label)

        self.time_label = QLabel()
        self.update_time()
        status.addPermanentWidget(self.time_label)

        # Timer for updating time
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)

    def center_window(self):
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )

    def apply_dark_theme(self):
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1a202c, stop:1 #2d3748);
            }
        """)

    def add_sample_victims(self):
        """Add sample victims for demonstration"""
        sample_victims = [
            {
                "id": "victim_001",
                "computer_name": "DESKTOP-ABC123",
                "ip": "*************",
                "username": "john_doe",
                "os": "Windows 10 Pro",
                "is_admin": True,
                "connected_time": "14:32:15"
            },
            {
                "id": "victim_002",
                "computer_name": "LAPTOP-XYZ789",
                "ip": "*************",
                "username": "sarah_smith",
                "os": "Windows 11 Home",
                "is_admin": False,
                "connected_time": "14:45:22"
            },
            {
                "id": "victim_003",
                "computer_name": "WORKSTATION-DEF456",
                "ip": "*************",
                "username": "admin",
                "os": "Windows Server 2019",
                "is_admin": True,
                "connected_time": "15:12:08"
            }
        ]

        for victim_data in sample_victims:
            self.add_victim(victim_data)

    def add_victim(self, victim_data):
        """Add a new victim to the list"""
        victim_card = VictimCard(victim_data)
        victim_card.clicked.connect(self.open_victim_control)

        # Insert at the beginning (before stretch)
        self.victims_layout.insertWidget(0, victim_card)

        # Update victim count
        self.victims[victim_data["id"]] = victim_data
        self.update_victim_count()

    def remove_victim(self, victim_id):
        """Remove a victim from the list"""
        if victim_id in self.victims:
            del self.victims[victim_id]
            self.update_victim_count()

            # Close control window if open
            if victim_id in self.control_windows:
                self.control_windows[victim_id].close()
                del self.control_windows[victim_id]

    def update_victim_count(self):
        """Update the victim count display"""
        count = len(self.victims)
        self.victim_count.setText(str(count))
        self.connection_label.setText(f"🔗 Connections: {count}")

    def update_time(self):
        """Update the time display"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_label.setText(f"🕐 {current_time}")

    def open_victim_control(self, victim_data):
        """Open control panel for selected victim"""
        victim_id = victim_data["id"]

        if victim_id in self.control_windows:
            # Bring existing window to front
            self.control_windows[victim_id].raise_()
            self.control_windows[victim_id].activateWindow()
        else:
            # Create new control window
            control_window = QDialog(self)
            control_window.setWindowTitle(f"Control Panel - {victim_data['computer_name']}")
            control_window.setMinimumSize(1000, 700)
            control_window.resize(1200, 800)

            # Add control panel
            layout = QVBoxLayout(control_window)
            control_panel = ControlPanel(victim_data)
            layout.addWidget(control_panel)

            # Store reference
            self.control_windows[victim_id] = control_window

            # Show window
            control_window.show()

    # Event handlers
    def refresh_victims(self):
        """Refresh the victims list"""
        self.logs_text.append(f"<span style='color: #00ffff;'>[{datetime.now().strftime('%H:%M:%S')}] Refreshing victim list...</span>")

    def start_server(self):
        """Start the server"""
        self.logs_text.append(f"<span style='color: #68d391;'>[{datetime.now().strftime('%H:%M:%S')}] Server started successfully</span>")

    def stop_server(self):
        """Stop the server"""
        self.logs_text.append(f"<span style='color: #ff6b6b;'>[{datetime.now().strftime('%H:%M:%S')}] Server stopped</span>")

    def open_settings(self):
        """Open settings dialog"""
        QMessageBox.information(self, "Settings", "Settings dialog will be implemented here")

    def open_analytics(self):
        """Open analytics window"""
        QMessageBox.information(self, "Analytics", "Analytics window will be implemented here")

    def open_security(self):
        """Open security settings"""
        QMessageBox.information(self, "Security", "Security settings will be implemented here")

    def export_data(self):
        """Export data"""
        QMessageBox.information(self, "Export", "Data export will be implemented here")

    def clear_logs(self):
        """Clear server logs"""
        self.logs_text.clear()
        self.logs_text.append(f"<span style='color: #ffd700;'>[{datetime.now().strftime('%H:%M:%S')}] Logs cleared</span>")

    def show_help(self):
        """Show help dialog"""
        QMessageBox.information(self, "Help", "Help documentation will be implemented here")

    def new_session(self):
        """Create new session"""
        pass

    def save_session(self):
        """Save current session"""
        pass

    def load_session(self):
        """Load saved session"""
        pass

    def open_payload_builder(self):
        """Open payload builder"""
        pass

    def open_network_scanner(self):
        """Open network scanner"""
        pass

    def open_traffic_monitor(self):
        """Open traffic monitor"""
        pass

    def show_documentation(self):
        """Show documentation"""
        pass

    def show_about(self):
        """Show about dialog"""
        about_text = """
        <h2 style='color: #00ffff;'>For3on CyberTrap</h2>
        <p style='color: #e2e8f0;'><b>Version:</b> 2.0 Professional</p>
        <p style='color: #e2e8f0;'><b>Developer:</b> For3on Security Team</p>
        <p style='color: #e2e8f0;'><b>Purpose:</b> Advanced Remote Access Tool for Cybersecurity Defense</p>
        <br>
        <p style='color: #ffd700;'><b>⚠️ Legal Notice:</b></p>
        <p style='color: #ff6b6b;'>This tool is designed for educational and authorized testing purposes only.</p>
        <p style='color: #ff6b6b;'>Unauthorized access to computer systems is illegal.</p>
        """

        msg = QMessageBox(self)
        msg.setWindowTitle("About For3on CyberTrap")
        msg.setText(about_text)
        msg.setStyleSheet("""
            QMessageBox {
                background: #1a202c;
                color: #e2e8f0;
            }
            QMessageBox QPushButton {
                background: #00ffff;
                color: #1a202c;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
                min-width: 80px;
            }
        """)
        msg.exec_()
