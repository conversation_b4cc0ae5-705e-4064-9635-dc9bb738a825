# -*- coding: utf-8 -*-
"""
For3on CyberTrap - Resources and Icons
Custom icons and resources for the application
"""

from PyQt5.QtGui import *
from PyQt5.QtCore import *

class IconFactory:
    """Factory class for creating custom icons"""
    
    @staticmethod
    def create_victim_icon(is_admin=False, is_online=True):
        """Create victim status icon"""
        pixmap = QPixmap(32, 32)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Base computer icon
        if is_online:
            painter.setBrush(QBrush(QColor(0, 255, 255, 150)))
            painter.setPen(QPen(QColor(0, 255, 255), 2))
        else:
            painter.setBrush(QBrush(QColor(128, 128, 128, 150)))
            painter.setPen(QPen(QColor(128, 128, 128), 2))
        
        # Monitor
        painter.drawRoundedRect(4, 8, 24, 16, 2, 2)
        
        # Stand
        painter.drawRect(14, 24, 4, 4)
        painter.drawRect(10, 28, 12, 2)
        
        # Admin crown
        if is_admin:
            painter.setBrush(QBrush(QColor(255, 215, 0)))
            painter.setPen(QPen(QColor(255, 215, 0), 1))
            
            # Crown points
            crown = QPolygon([
                QPoint(12, 4), QPoint(16, 2), QPoint(20, 4),
                QPoint(22, 6), QPoint(10, 6)
            ])
            painter.drawPolygon(crown)
        
        painter.end()
        return QIcon(pixmap)
    
    @staticmethod
    def create_status_icon(status="online"):
        """Create status indicator icon"""
        pixmap = QPixmap(16, 16)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        colors = {
            "online": QColor(104, 211, 145),    # Green
            "offline": QColor(255, 107, 107),   # Red
            "connecting": QColor(255, 215, 0),  # Yellow
            "error": QColor(255, 107, 107)      # Red
        }
        
        color = colors.get(status, colors["offline"])
        painter.setBrush(QBrush(color))
        painter.setPen(QPen(color.darker(120), 1))
        painter.drawEllipse(2, 2, 12, 12)
        
        painter.end()
        return QIcon(pixmap)
    
    @staticmethod
    def create_command_icon():
        """Create command terminal icon"""
        pixmap = QPixmap(24, 24)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Terminal window
        painter.setBrush(QBrush(QColor(26, 32, 44)))
        painter.setPen(QPen(QColor(0, 255, 255), 2))
        painter.drawRoundedRect(2, 2, 20, 20, 3, 3)
        
        # Command prompt
        painter.setPen(QPen(QColor(0, 255, 0), 1))
        painter.drawText(6, 12, ">")
        painter.drawLine(10, 11, 18, 11)
        painter.drawLine(10, 15, 15, 15)
        
        painter.end()
        return QIcon(pixmap)
    
    @staticmethod
    def create_file_icon():
        """Create file manager icon"""
        pixmap = QPixmap(24, 24)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Folder
        painter.setBrush(QBrush(QColor(255, 193, 7, 200)))
        painter.setPen(QPen(QColor(255, 193, 7), 1))
        
        # Folder tab
        painter.drawRoundedRect(3, 6, 8, 3, 1, 1)
        # Folder body
        painter.drawRoundedRect(3, 9, 18, 12, 2, 2)
        
        painter.end()
        return QIcon(pixmap)
    
    @staticmethod
    def create_screenshot_icon():
        """Create screenshot icon"""
        pixmap = QPixmap(24, 24)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Camera body
        painter.setBrush(QBrush(QColor(96, 125, 139)))
        painter.setPen(QPen(QColor(96, 125, 139), 1))
        painter.drawRoundedRect(4, 8, 16, 12, 2, 2)
        
        # Lens
        painter.setBrush(QBrush(QColor(33, 33, 33)))
        painter.drawEllipse(8, 11, 8, 6)
        
        # Lens center
        painter.setBrush(QBrush(QColor(0, 255, 255)))
        painter.drawEllipse(10, 13, 4, 2)
        
        # Flash
        painter.setBrush(QBrush(QColor(255, 255, 255)))
        painter.drawRect(6, 6, 2, 2)
        
        painter.end()
        return QIcon(pixmap)
    
    @staticmethod
    def create_password_icon():
        """Create password/key icon"""
        pixmap = QPixmap(24, 24)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Key head (circle)
        painter.setBrush(QBrush(QColor(255, 215, 0)))
        painter.setPen(QPen(QColor(255, 215, 0), 2))
        painter.drawEllipse(3, 3, 8, 8)
        
        # Key hole
        painter.setBrush(QBrush(QColor(26, 32, 44)))
        painter.drawEllipse(5, 5, 4, 4)
        
        # Key shaft
        painter.setPen(QPen(QColor(255, 215, 0), 3))
        painter.drawLine(11, 7, 20, 7)
        
        # Key teeth
        painter.drawLine(18, 7, 18, 10)
        painter.drawLine(20, 7, 20, 9)
        
        painter.end()
        return QIcon(pixmap)
    
    @staticmethod
    def create_system_icon():
        """Create system/settings icon"""
        pixmap = QPixmap(24, 24)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Gear outline
        painter.setBrush(Qt.NoBrush)
        painter.setPen(QPen(QColor(160, 174, 192), 2))
        
        # Outer gear
        painter.drawEllipse(4, 4, 16, 16)
        
        # Gear teeth (simplified)
        for angle in range(0, 360, 45):
            x1 = 12 + 10 * cos(radians(angle))
            y1 = 12 + 10 * sin(radians(angle))
            x2 = 12 + 8 * cos(radians(angle))
            y2 = 12 + 8 * sin(radians(angle))
            painter.drawLine(x1, y1, x2, y2)
        
        # Center circle
        painter.setBrush(QBrush(QColor(0, 255, 255)))
        painter.drawEllipse(9, 9, 6, 6)
        
        painter.end()
        return QIcon(pixmap)
    
    @staticmethod
    def create_disconnect_icon():
        """Create disconnect icon"""
        pixmap = QPixmap(24, 24)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Broken connection
        painter.setPen(QPen(QColor(255, 107, 107), 3))
        
        # Left part
        painter.drawLine(3, 12, 9, 12)
        painter.drawEllipse(2, 10, 4, 4)
        
        # Right part
        painter.drawLine(15, 12, 21, 12)
        painter.drawEllipse(18, 10, 4, 4)
        
        # Break indicator
        painter.setPen(QPen(QColor(255, 215, 0), 2))
        painter.drawLine(10, 8, 14, 16)
        painter.drawLine(10, 16, 14, 8)
        
        painter.end()
        return QIcon(pixmap)

def cos(angle):
    """Cosine function for angle in radians"""
    import math
    return math.cos(angle)

def sin(angle):
    """Sine function for angle in radians"""
    import math
    return math.sin(angle)

def radians(degrees):
    """Convert degrees to radians"""
    import math
    return math.radians(degrees)

class ThemeManager:
    """Manages application themes and colors"""
    
    # Color palette
    COLORS = {
        'primary': '#00ffff',
        'secondary': '#4dd0e1',
        'background': '#1a202c',
        'surface': '#2d3748',
        'surface_light': '#4a5568',
        'text_primary': '#e2e8f0',
        'text_secondary': '#a0aec0',
        'success': '#68d391',
        'warning': '#ffd700',
        'error': '#ff6b6b',
        'admin': '#ffd700'
    }
    
    @classmethod
    def get_color(cls, name):
        """Get color by name"""
        return cls.COLORS.get(name, '#ffffff')
    
    @classmethod
    def create_gradient(cls, color1, color2, direction='vertical'):
        """Create gradient string for stylesheets"""
        if direction == 'vertical':
            return f"qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {color1}, stop:1 {color2})"
        else:
            return f"qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 {color1}, stop:1 {color2})"
    
    @classmethod
    def get_button_style(cls, variant='primary'):
        """Get button stylesheet"""
        if variant == 'primary':
            return f"""
                QPushButton {{
                    background: {cls.create_gradient(cls.COLORS['surface'], cls.COLORS['background'])};
                    border: 2px solid {cls.COLORS['surface_light']};
                    border-radius: 8px;
                    color: {cls.COLORS['text_primary']};
                    font-weight: bold;
                    font-size: 12px;
                    padding: 8px 16px;
                    min-height: 30px;
                }}
                QPushButton:hover {{
                    background: {cls.create_gradient(cls.COLORS['surface_light'], cls.COLORS['surface'])};
                    border: 2px solid {cls.COLORS['primary']};
                    color: {cls.COLORS['primary']};
                }}
                QPushButton:pressed {{
                    background: {cls.create_gradient(cls.COLORS['background'], cls.COLORS['surface'])};
                }}
            """
        elif variant == 'danger':
            return f"""
                QPushButton {{
                    background: {cls.create_gradient('#e53e3e', '#c53030')};
                    border: 2px solid #fc8181;
                    color: white;
                    font-weight: bold;
                    padding: 10px 20px;
                    border-radius: 8px;
                }}
                QPushButton:hover {{
                    background: {cls.create_gradient('#fc8181', '#e53e3e')};
                }}
            """
        
        return ""
