# -*- coding: utf-8 -*-
"""
For3on CyberTrap - Animated Splash Screen
Professional welcome screen with animations
"""

import sys
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class AnimatedLogo(QLabel):
    """Animated logo with rotation and glow effects"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(120, 120)
        self.angle = 0
        
        # Create logo pixmap
        self.create_logo()
        
        # Rotation animation
        self.rotation_timer = QTimer()
        self.rotation_timer.timeout.connect(self.rotate_logo)
        self.rotation_timer.start(50)  # 50ms for smooth rotation
        
        # Glow effect
        self.glow_effect = QGraphicsDropShadowEffect()
        self.glow_effect.setBlurRadius(30)
        self.glow_effect.setColor(QColor(0, 255, 255, 150))
        self.glow_effect.setOffset(0, 0)
        self.setGraphicsEffect(self.glow_effect)
        
        # Pulsing glow animation
        self.glow_animation = QPropertyAnimation(self.glow_effect, b"blurRadius")
        self.glow_animation.setDuration(2000)
        self.glow_animation.setStartValue(20)
        self.glow_animation.setEndValue(40)
        self.glow_animation.setEasingCurve(QEasingCurve.InOutSine)
        self.glow_animation.setLoopCount(-1)
        self.glow_animation.start()
    
    def create_logo(self):
        """Create the logo pixmap"""
        pixmap = QPixmap(120, 120)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Outer ring
        painter.setPen(QPen(QColor(0, 255, 255), 4))
        painter.setBrush(Qt.NoBrush)
        painter.drawEllipse(10, 10, 100, 100)
        
        # Inner circle
        painter.setBrush(QBrush(QColor(0, 255, 255, 100)))
        painter.drawEllipse(30, 30, 60, 60)
        
        # Center symbol
        painter.setPen(QPen(QColor(255, 255, 255), 3))
        painter.drawLine(60, 40, 60, 80)
        painter.drawLine(40, 60, 80, 60)
        
        # Corner triangles
        painter.setBrush(QBrush(QColor(255, 215, 0)))
        triangle1 = QPolygon([QPoint(20, 20), QPoint(35, 20), QPoint(20, 35)])
        triangle2 = QPolygon([QPoint(100, 20), QPoint(85, 20), QPoint(100, 35)])
        triangle3 = QPolygon([QPoint(20, 100), QPoint(35, 100), QPoint(20, 85)])
        triangle4 = QPolygon([QPoint(100, 100), QPoint(85, 100), QPoint(100, 85)])
        
        painter.drawPolygon(triangle1)
        painter.drawPolygon(triangle2)
        painter.drawPolygon(triangle3)
        painter.drawPolygon(triangle4)
        
        painter.end()
        
        self.original_pixmap = pixmap
        self.setPixmap(pixmap)
    
    def rotate_logo(self):
        """Rotate the logo"""
        self.angle += 2
        if self.angle >= 360:
            self.angle = 0
        
        # Create rotated pixmap
        transform = QTransform()
        transform.translate(60, 60)
        transform.rotate(self.angle)
        transform.translate(-60, -60)
        
        rotated_pixmap = self.original_pixmap.transformed(transform, Qt.SmoothTransformation)
        self.setPixmap(rotated_pixmap)

class TypewriterLabel(QLabel):
    """Label with typewriter animation effect"""
    
    finished = pyqtSignal()
    
    def __init__(self, text, parent=None):
        super().__init__(parent)
        self.full_text = text
        self.current_text = ""
        self.current_index = 0
        
        # Typewriter timer
        self.typewriter_timer = QTimer()
        self.typewriter_timer.timeout.connect(self.add_character)
        
    def start_animation(self, delay=0):
        """Start the typewriter animation"""
        QTimer.singleShot(delay, self.begin_typing)
    
    def begin_typing(self):
        """Begin the typing animation"""
        self.typewriter_timer.start(100)  # 100ms per character
    
    def add_character(self):
        """Add next character"""
        if self.current_index < len(self.full_text):
            self.current_text += self.full_text[self.current_index]
            self.setText(self.current_text)
            self.current_index += 1
        else:
            self.typewriter_timer.stop()
            self.finished.emit()

class ProgressBar(QProgressBar):
    """Custom animated progress bar"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setRange(0, 100)
        self.setValue(0)
        self.setTextVisible(False)
        self.setFixedHeight(8)
        
        self.setStyleSheet("""
            QProgressBar {
                border: 2px solid #4a5568;
                border-radius: 4px;
                background: #1a202c;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #00ffff, stop:0.5 #0080ff, stop:1 #00ffff);
                border-radius: 2px;
            }
        """)
        
        # Animated progress
        self.animation = QPropertyAnimation(self, b"value")
        self.animation.setDuration(3000)
        self.animation.setStartValue(0)
        self.animation.setEndValue(100)
        self.animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def start_progress(self, delay=0):
        """Start progress animation"""
        QTimer.singleShot(delay, self.animation.start)

class SplashScreen(QSplashScreen):
    """Professional animated splash screen"""
    
    finished = pyqtSignal()
    
    def __init__(self):
        # Create splash pixmap
        pixmap = QPixmap(600, 400)
        pixmap.fill(Qt.transparent)
        
        super().__init__(pixmap)
        self.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        
        self.setup_ui()
        self.start_animations()
    
    def setup_ui(self):
        """Setup the splash screen UI"""
        # Main widget
        widget = QWidget()
        widget.setFixedSize(600, 400)
        widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0f0f23, stop:0.5 #1a1a2e, stop:1 #16213e);
                border: 3px solid #00ffff;
                border-radius: 20px;
            }
        """)
        
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(40, 40, 40, 40)
        layout.setSpacing(20)
        
        # Logo section
        logo_layout = QHBoxLayout()
        logo_layout.addStretch()
        
        self.logo = AnimatedLogo()
        logo_layout.addWidget(self.logo)
        
        logo_layout.addStretch()
        layout.addLayout(logo_layout)
        
        # Title
        self.title = TypewriterLabel("For3on CyberTrap")
        self.title.setAlignment(Qt.AlignCenter)
        self.title.setStyleSheet("""
            QLabel {
                color: #00ffff;
                font-size: 32px;
                font-weight: bold;
                font-family: 'Arial Black', sans-serif;
                background: transparent;
                text-shadow: 0 0 10px #00ffff;
            }
        """)
        layout.addWidget(self.title)
        
        # Subtitle
        self.subtitle = TypewriterLabel("Professional Remote Access Tool")
        self.subtitle.setAlignment(Qt.AlignCenter)
        self.subtitle.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: normal;
                background: transparent;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(self.subtitle)
        
        # Version and author
        self.version = TypewriterLabel("Version 2.0 Professional | By For3on Security Team")
        self.version.setAlignment(Qt.AlignCenter)
        self.version.setStyleSheet("""
            QLabel {
                color: #a0aec0;
                font-size: 12px;
                background: transparent;
                margin-bottom: 30px;
            }
        """)
        layout.addWidget(self.version)
        
        # Loading text
        self.loading_text = TypewriterLabel("Initializing CyberTrap Systems...")
        self.loading_text.setAlignment(Qt.AlignCenter)
        self.loading_text.setStyleSheet("""
            QLabel {
                color: #68d391;
                font-size: 14px;
                font-weight: bold;
                background: transparent;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(self.loading_text)
        
        # Progress bar
        self.progress = ProgressBar()
        layout.addWidget(self.progress)
        
        # Warning text
        self.warning = TypewriterLabel("⚠️ For Educational and Authorized Testing Only ⚠️")
        self.warning.setAlignment(Qt.AlignCenter)
        self.warning.setStyleSheet("""
            QLabel {
                color: #ffd700;
                font-size: 11px;
                font-weight: bold;
                background: transparent;
                margin-top: 20px;
            }
        """)
        layout.addWidget(self.warning)
        
        # Set the widget as the splash screen content
        self.setPixmap(self.grab_widget_pixmap(widget))
    
    def grab_widget_pixmap(self, widget):
        """Grab pixmap from widget"""
        pixmap = QPixmap(widget.size())
        widget.render(pixmap)
        return pixmap
    
    def start_animations(self):
        """Start all animations in sequence"""
        # Title animation
        self.title.start_animation(500)
        
        # Subtitle animation
        self.title.finished.connect(lambda: self.subtitle.start_animation(300))
        
        # Version animation
        self.subtitle.finished.connect(lambda: self.version.start_animation(200))
        
        # Loading text animation
        self.version.finished.connect(lambda: self.loading_text.start_animation(500))
        
        # Progress bar animation
        self.loading_text.finished.connect(lambda: self.progress.start_progress(300))
        
        # Warning text animation
        self.progress.animation.finished.connect(lambda: self.warning.start_animation(200))
        
        # Finish splash screen
        self.warning.finished.connect(self.finish_splash)
    
    def finish_splash(self):
        """Finish the splash screen"""
        QTimer.singleShot(1000, self.close_splash)
    
    def close_splash(self):
        """Close splash screen and emit finished signal"""
        self.finished.emit()
        self.close()

class LoadingDialog(QDialog):
    """Loading dialog for operations"""
    
    def __init__(self, title="Loading...", message="Please wait...", parent=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setFixedSize(400, 150)
        self.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)
        
        self.setup_ui(message)
        self.center_window()
    
    def setup_ui(self, message):
        """Setup loading dialog UI"""
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1a202c, stop:1 #2d3748);
                border: 2px solid #00ffff;
                border-radius: 10px;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # Loading icon
        icon_layout = QHBoxLayout()
        icon_layout.addStretch()
        
        loading_icon = AnimatedLogo()
        loading_icon.setFixedSize(60, 60)
        icon_layout.addWidget(loading_icon)
        
        icon_layout.addStretch()
        layout.addLayout(icon_layout)
        
        # Message
        message_label = QLabel(message)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setStyleSheet("""
            QLabel {
                color: #e2e8f0;
                font-size: 14px;
                font-weight: bold;
                background: transparent;
            }
        """)
        layout.addWidget(message_label)
        
        # Progress bar
        self.progress = ProgressBar()
        layout.addWidget(self.progress)
    
    def center_window(self):
        """Center the dialog"""
        if self.parent():
            parent_geo = self.parent().geometry()
            x = parent_geo.x() + (parent_geo.width() - self.width()) // 2
            y = parent_geo.y() + (parent_geo.height() - self.height()) // 2
            self.move(x, y)
    
    def start_loading(self):
        """Start loading animation"""
        self.progress.start_progress()
        self.show()
    
    def finish_loading(self):
        """Finish loading"""
        self.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Show splash screen
    splash = SplashScreen()
    splash.show()
    
    sys.exit(app.exec_())
