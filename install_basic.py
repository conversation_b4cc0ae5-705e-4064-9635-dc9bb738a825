#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CyberTrap For3on - Basic Installation
Install only essential dependencies for core functionality
"""

import sys
import subprocess
import importlib

def install_package(package_name, import_name=None):
    """Install a single package"""
    if import_name is None:
        import_name = package_name
    
    try:
        # Try to import first
        importlib.import_module(import_name)
        print(f"✅ {package_name} already installed")
        return True
    except ImportError:
        pass
    
    try:
        print(f"📦 Installing {package_name}...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", package_name, "--user"
        ])
        print(f"✅ {package_name} installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package_name}: {e}")
        return False

def main():
    """Install basic dependencies"""
    print("🔥 CyberTrap For3on - Basic Installation")
    print("=" * 50)
    print("Installing essential dependencies only...")
    print()
    
    # Essential packages only
    essential_packages = [
        ("cryptography", "cryptography"),
        ("requests", "requests"),
        ("psutil", "psutil"),
    ]
    
    # Optional packages (install if possible)
    optional_packages = [
        ("PyQt5", "PyQt5"),
        ("pillow", "PIL"),
        ("pyautogui", "pyautogui"),
        ("pynput", "pynput"),
        ("opencv-python", "cv2"),
        ("pyinstaller", "PyInstaller"),
    ]
    
    print("📦 Installing essential packages...")
    essential_success = 0
    for package, import_name in essential_packages:
        if install_package(package, import_name):
            essential_success += 1
    
    print(f"\n✅ Essential packages: {essential_success}/{len(essential_packages)} installed")
    
    print("\n📦 Installing optional packages...")
    optional_success = 0
    for package, import_name in optional_packages:
        if install_package(package, import_name):
            optional_success += 1
    
    print(f"\n✅ Optional packages: {optional_success}/{len(optional_packages)} installed")
    
    if essential_success == len(essential_packages):
        print("\n🎉 Core functionality ready!")
        print("You can now run: python quick_demo.py")
    else:
        print("\n⚠️  Some essential packages failed to install")
        print("Try running: pip install --user cryptography requests psutil")
    
    print("\n📚 Next steps:")
    print("1. Run demo: python quick_demo.py")
    print("2. Test server: python run_server.py")
    print("3. Read docs: README.md")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Installation interrupted")
    except Exception as e:
        print(f"\n❌ Installation failed: {e}")
