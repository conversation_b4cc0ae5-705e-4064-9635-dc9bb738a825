#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CyberTrap For3on - Quick Demo
Demonstration of core functionality without GUI dependencies
"""

import sys
import os
import time
import json
import base64
import socket
import threading
from pathlib import Path

# Add project paths
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent / "shared"))

def demo_encryption():
    """Demonstrate encryption functionality"""
    print("🔐 Testing Encryption Module")
    print("-" * 30)
    
    try:
        from shared.encryption import crypto_manager
        
        # Generate keys
        print("   Generating RSA keys...")
        crypto_manager.generate_rsa_keys()
        
        print("   Generating AES key...")
        crypto_manager.generate_aes_key()
        
        # Test encryption
        test_data = "Hello, CyberTrap For3on! 🔥"
        print(f"   Original: {test_data}")
        
        encrypted = crypto_manager.encrypt_data(test_data)
        print(f"   Encrypted: {encrypted[:50]}...")
        
        decrypted = crypto_manager.decrypt_data(encrypted)
        print(f"   Decrypted: {decrypted}")
        
        if decrypted == test_data:
            print("   ✅ Encryption test PASSED")
        else:
            print("   ❌ Encryption test FAILED")
            
        return True
        
    except Exception as e:
        print(f"   ❌ Encryption test failed: {e}")
        return False

def demo_protocol():
    """Demonstrate protocol functionality"""
    print("\n📡 Testing Protocol Module")
    print("-" * 30)
    
    try:
        from shared.protocol import Protocol, MessageType
        
        # Test message packing
        print("   Testing message packing...")
        test_message = Protocol.pack_message(
            MessageType.HEARTBEAT, 
            {"status": "alive", "timestamp": time.time()}
        )
        
        if test_message:
            print(f"   Message size: {len(test_message)} bytes")
            print("   ✅ Protocol test PASSED")
        else:
            print("   ❌ Protocol test FAILED")
            
        return True
        
    except Exception as e:
        print(f"   ❌ Protocol test failed: {e}")
        return False

def demo_config():
    """Demonstrate configuration functionality"""
    print("\n⚙️  Testing Configuration Module")
    print("-" * 30)
    
    try:
        from shared.config import Config
        
        print(f"   App Name: {Config.APP_NAME}")
        print(f"   Version: {Config.APP_VERSION}")
        print(f"   Server Host: {Config.SERVER_HOST}")
        print(f"   Server Port: {Config.SERVER_PORT}")
        
        # Test directory creation
        Config.create_directories()
        print("   Directories created")
        
        print("   ✅ Configuration test PASSED")
        return True
        
    except Exception as e:
        print(f"   ❌ Configuration test failed: {e}")
        return False

def demo_system_info():
    """Demonstrate system information collection"""
    print("\n🖥️  Testing System Information")
    print("-" * 30)
    
    try:
        # Basic system info without dependencies
        import platform
        import socket
        
        print(f"   Platform: {platform.system()} {platform.release()}")
        print(f"   Architecture: {platform.architecture()[0]}")
        print(f"   Processor: {platform.processor()}")
        print(f"   Hostname: {socket.gethostname()}")
        print(f"   Python: {sys.version.split()[0]}")
        
        print("   ✅ System info test PASSED")
        return True
        
    except Exception as e:
        print(f"   ❌ System info test failed: {e}")
        return False

def demo_simple_server():
    """Demonstrate simple server functionality"""
    print("\n🚀 Testing Simple Server")
    print("-" * 30)
    
    try:
        # Create a simple test server
        server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        
        # Try to bind to port
        host = "127.0.0.1"
        port = 4444
        
        try:
            server_socket.bind((host, port))
            server_socket.listen(1)
            print(f"   Server bound to {host}:{port}")
            
            # Close immediately for demo
            server_socket.close()
            print("   ✅ Server test PASSED")
            return True
            
        except OSError as e:
            if "already in use" in str(e).lower():
                print(f"   Port {port} is already in use (this is normal)")
                print("   ✅ Server test PASSED")
                return True
            else:
                raise
                
    except Exception as e:
        print(f"   ❌ Server test failed: {e}")
        return False
    finally:
        try:
            server_socket.close()
        except:
            pass

def demo_file_operations():
    """Demonstrate file operations"""
    print("\n📁 Testing File Operations")
    print("-" * 30)
    
    try:
        # Test file creation and reading
        test_file = Path("test_demo.txt")
        test_content = "CyberTrap For3on Demo File"
        
        # Write test file
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        print(f"   Created test file: {test_file}")
        
        # Read test file
        with open(test_file, 'r', encoding='utf-8') as f:
            read_content = f.read()
        
        if read_content == test_content:
            print("   File read/write successful")
        
        # Test base64 encoding
        encoded = base64.b64encode(test_content.encode()).decode()
        decoded = base64.b64decode(encoded).decode()
        
        if decoded == test_content:
            print("   Base64 encoding/decoding successful")
        
        # Cleanup
        test_file.unlink()
        print("   Test file cleaned up")
        
        print("   ✅ File operations test PASSED")
        return True
        
    except Exception as e:
        print(f"   ❌ File operations test failed: {e}")
        return False

def show_project_structure():
    """Show project structure"""
    print("\n📂 Project Structure")
    print("-" * 30)
    
    def print_tree(directory, prefix="", max_depth=2, current_depth=0):
        if current_depth >= max_depth:
            return
            
        try:
            items = sorted(directory.iterdir())
            dirs = [item for item in items if item.is_dir() and not item.name.startswith('.')]
            files = [item for item in items if item.is_file() and not item.name.startswith('.')]
            
            # Print directories first
            for i, item in enumerate(dirs):
                is_last = i == len(dirs) - 1 and len(files) == 0
                print(f"{prefix}{'└── ' if is_last else '├── '}{item.name}/")
                
                extension = "    " if is_last else "│   "
                print_tree(item, prefix + extension, max_depth, current_depth + 1)
            
            # Print files
            for i, item in enumerate(files):
                is_last = i == len(files) - 1
                print(f"{prefix}{'└── ' if is_last else '├── '}{item.name}")
                
        except PermissionError:
            pass
    
    project_root = Path(__file__).parent
    print(f"   {project_root.name}/")
    print_tree(project_root)

def show_usage_instructions():
    """Show usage instructions"""
    print("\n📖 Usage Instructions")
    print("=" * 50)
    print()
    print("🚀 Quick Start:")
    print("   1. Install dependencies: pip install -r requirements.txt")
    print("   2. Run setup: python setup.py")
    print("   3. Start server: python run_server.py")
    print("   4. Test client: python run_client.py")
    print()
    print("🔧 Development Tools:")
    print("   - python dev_tools.py clean    # Clean project")
    print("   - python dev_tools.py test     # Run tests")
    print("   - python dev_tools.py build    # Build executable")
    print("   - python test_connection.py    # Test connectivity")
    print()
    print("📚 Documentation:")
    print("   - README.md      # Complete documentation")
    print("   - INSTALL.md     # Installation guide")
    print("   - LICENSE        # License information")
    print()
    print("⚠️  Important:")
    print("   This tool is for educational and authorized testing only!")
    print("   Always obtain proper permission before testing.")

def main():
    """Main demo function"""
    print("🔥 CyberTrap For3on - Quick Demo")
    print("=" * 50)
    print("🛡️  Advanced RAT for Cybersecurity Defense")
    print("👥 Developed by For3on Security Team")
    print()
    
    # Run all demos
    tests = [
        demo_config,
        demo_encryption,
        demo_protocol,
        demo_system_info,
        demo_simple_server,
        demo_file_operations
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"   ❌ Test failed with exception: {e}")
    
    # Show results
    print(f"\n📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("✅ All core functionality working!")
    else:
        print("⚠️  Some tests failed - check dependencies")
    
    # Show project structure
    show_project_structure()
    
    # Show usage instructions
    show_usage_instructions()
    
    print("\n" + "=" * 50)
    print("🎉 Demo completed!")
    print("Ready to start your cybersecurity defense journey!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
