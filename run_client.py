#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CyberTrap For3on - Quick Client Launcher
Quick launcher for testing the CyberTrap client
"""

import sys
import os
from pathlib import Path

# Add client directory to path
client_dir = Path(__file__).parent / "client"
sys.path.insert(0, str(client_dir))

def main():
    """Launch the CyberTrap client for testing"""
    try:
        print("🔥 CyberTrap For3on - Client (Test Mode)")
        print("=" * 50)
        print("⚠️  WARNING: This is for testing purposes only!")
        print("🚀 Starting client...")
        
        # Import and run the client
        from client import CyberTrapClient
        
        # Create client with default settings
        client = CyberTrapClient()
        client.run()
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure all dependencies are installed:")
        print("   pip install -r requirements.txt")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n🛑 Client stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Error starting client: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
