#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
For3on CyberTrap - GUI Launcher
Launch the professional RAT interface
"""

import sys
import os
from pathlib import Path

# Add GUI directory to path
gui_dir = Path(__file__).parent / "gui"
sys.path.insert(0, str(gui_dir))

def check_dependencies():
    """Check if required dependencies are installed"""
    missing_deps = []
    
    try:
        import PyQt5
    except ImportError:
        missing_deps.append("PyQt5")
    
    if missing_deps:
        print("❌ Missing dependencies:")
        for dep in missing_deps:
            print(f"   - {dep}")
        print("\n💡 Install with: pip install PyQt5")
        return False
    
    return True

def main():
    """Main launcher function"""
    print("🔥 For3on CyberTrap - Professional RAT Interface")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        input("Press Enter to exit...")
        return 1
    
    try:
        # Import and run the application
        from cybertrap_app import main as app_main
        
        print("🚀 Starting GUI application...")
        return app_main()
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure you're running from the correct directory")
        input("Press Enter to exit...")
        return 1
    
    except Exception as e:
        print(f"❌ Application error: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 Application interrupted by user")
        sys.exit(0)
