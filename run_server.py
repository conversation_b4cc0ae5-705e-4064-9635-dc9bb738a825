#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CyberTrap For3on - Quick Server Launcher
Quick launcher for the CyberTrap server
"""

import sys
import os
from pathlib import Path

# Add server directory to path
server_dir = Path(__file__).parent / "server"
sys.path.insert(0, str(server_dir))

def main():
    """Launch the CyberTrap server"""
    try:
        print("🔥 CyberTrap For3on - Advanced RAT Server")
        print("=" * 50)
        print("🚀 Starting server GUI...")
        
        # Import and run the main application
        from main import main as server_main
        server_main()
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure all dependencies are installed:")
        print("   pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
