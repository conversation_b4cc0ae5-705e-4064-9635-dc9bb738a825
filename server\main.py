# -*- coding: utf-8 -*-
"""
CyberTrap For3on - Main Server GUI
Advanced RAT Server with PyQt5 Interface
"""

import sys
import os
import json
import base64
from datetime import datetime
from pathlib import Path

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from server_core import CyberTrapServer
from shared.config import Config
from shared.protocol import MessageType
from ui.main_window import MainWindow

class CyberTrapGUI(QApplication):
    """Main GUI application"""
    
    def __init__(self, argv):
        super().__init__(argv)
        
        # Set application properties
        self.setApplicationName("CyberTrap For3on")
        self.setApplicationVersion("1.0.0")
        self.setOrganizationName("For3on Security Team")
        
        # Set dark theme
        self.setStyle("Fusion")
        self.apply_dark_theme()
        
        # Initialize server
        self.server = CyberTrapServer()
        
        # Setup server callbacks
        self.server.on_client_connected = self.on_client_connected
        self.server.on_client_disconnected = self.on_client_disconnected
        self.server.on_client_message = self.on_client_message
        self.server.on_server_log = self.on_server_log
        
        # Create main window
        self.main_window = MainWindow(self.server)
        
        # Connect signals
        self.main_window.start_server_signal.connect(self.start_server)
        self.main_window.stop_server_signal.connect(self.stop_server)
        
    def apply_dark_theme(self):
        """Apply dark theme to application"""
        dark_palette = QPalette()
        
        # Window colors
        dark_palette.setColor(QPalette.Window, QColor(53, 53, 53))
        dark_palette.setColor(QPalette.WindowText, QColor(255, 255, 255))
        
        # Base colors
        dark_palette.setColor(QPalette.Base, QColor(25, 25, 25))
        dark_palette.setColor(QPalette.AlternateBase, QColor(53, 53, 53))
        
        # Tooltip colors
        dark_palette.setColor(QPalette.ToolTipBase, QColor(0, 0, 0))
        dark_palette.setColor(QPalette.ToolTipText, QColor(255, 255, 255))
        
        # Text colors
        dark_palette.setColor(QPalette.Text, QColor(255, 255, 255))
        
        # Button colors
        dark_palette.setColor(QPalette.Button, QColor(53, 53, 53))
        dark_palette.setColor(QPalette.ButtonText, QColor(255, 255, 255))
        
        # Highlight colors
        dark_palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
        dark_palette.setColor(QPalette.HighlightedText, QColor(0, 0, 0))
        
        self.setPalette(dark_palette)
        
        # Set stylesheet for additional styling
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
            }
            
            QMenuBar {
                background-color: #3c3c3c;
                color: white;
                border-bottom: 1px solid #555;
            }
            
            QMenuBar::item {
                background-color: transparent;
                padding: 4px 8px;
            }
            
            QMenuBar::item:selected {
                background-color: #555;
            }
            
            QMenu {
                background-color: #3c3c3c;
                color: white;
                border: 1px solid #555;
            }
            
            QMenu::item:selected {
                background-color: #555;
            }
            
            QStatusBar {
                background-color: #3c3c3c;
                color: white;
                border-top: 1px solid #555;
            }
            
            QToolBar {
                background-color: #3c3c3c;
                border: none;
                spacing: 3px;
            }
            
            QToolButton {
                background-color: #555;
                border: 1px solid #777;
                padding: 5px;
                border-radius: 3px;
            }
            
            QToolButton:hover {
                background-color: #666;
            }
            
            QToolButton:pressed {
                background-color: #444;
            }
            
            QTabWidget::pane {
                border: 1px solid #555;
                background-color: #2b2b2b;
            }
            
            QTabBar::tab {
                background-color: #3c3c3c;
                color: white;
                padding: 8px 16px;
                border: 1px solid #555;
                border-bottom: none;
            }
            
            QTabBar::tab:selected {
                background-color: #2b2b2b;
                border-bottom: 1px solid #2b2b2b;
            }
            
            QTabBar::tab:hover {
                background-color: #555;
            }
            
            QTreeWidget {
                background-color: #2b2b2b;
                color: white;
                border: 1px solid #555;
                selection-background-color: #42a5f5;
            }
            
            QTreeWidget::item {
                padding: 4px;
                border-bottom: 1px solid #444;
            }
            
            QTreeWidget::item:selected {
                background-color: #42a5f5;
                color: white;
            }
            
            QTreeWidget::item:hover {
                background-color: #555;
            }
            
            QTextEdit {
                background-color: #1e1e1e;
                color: #f0f0f0;
                border: 1px solid #555;
                font-family: 'Consolas', 'Monaco', monospace;
            }
            
            QLineEdit {
                background-color: #3c3c3c;
                color: white;
                border: 1px solid #555;
                padding: 4px;
                border-radius: 3px;
            }
            
            QLineEdit:focus {
                border: 2px solid #42a5f5;
            }
            
            QPushButton {
                background-color: #555;
                color: white;
                border: 1px solid #777;
                padding: 6px 12px;
                border-radius: 3px;
                font-weight: bold;
            }
            
            QPushButton:hover {
                background-color: #666;
            }
            
            QPushButton:pressed {
                background-color: #444;
            }
            
            QPushButton:disabled {
                background-color: #333;
                color: #666;
            }
            
            QGroupBox {
                color: white;
                border: 2px solid #555;
                border-radius: 5px;
                margin-top: 10px;
                font-weight: bold;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            
            QScrollBar:vertical {
                background-color: #3c3c3c;
                width: 12px;
                border-radius: 6px;
            }
            
            QScrollBar::handle:vertical {
                background-color: #555;
                border-radius: 6px;
                min-height: 20px;
            }
            
            QScrollBar::handle:vertical:hover {
                background-color: #666;
            }
            
            QScrollBar:horizontal {
                background-color: #3c3c3c;
                height: 12px;
                border-radius: 6px;
            }
            
            QScrollBar::handle:horizontal {
                background-color: #555;
                border-radius: 6px;
                min-width: 20px;
            }
            
            QScrollBar::handle:horizontal:hover {
                background-color: #666;
            }
        """)
    
    def start_server(self):
        """Start the server"""
        if self.server.start_server():
            self.main_window.update_server_status(True)
        else:
            QMessageBox.critical(
                self.main_window,
                "Server Error",
                "Failed to start server. Check if port is already in use."
            )
    
    def stop_server(self):
        """Stop the server"""
        self.server.stop_server()
        self.main_window.update_server_status(False)
    
    def on_client_connected(self, client_id, client_info):
        """Handle client connection"""
        self.main_window.add_client(client_id, client_info)
    
    def on_client_disconnected(self, client_id, client_info):
        """Handle client disconnection"""
        self.main_window.remove_client(client_id)
    
    def on_client_message(self, client_id, client_info, message):
        """Handle client message"""
        self.main_window.handle_client_message(client_id, message)
    
    def on_server_log(self, log_message, level):
        """Handle server log"""
        self.main_window.add_log_message(log_message, level)
    
    def run(self):
        """Run the application"""
        self.main_window.show()
        return self.exec_()

def main():
    """Main entry point"""
    app = CyberTrapGUI(sys.argv)
    
    # Set window icon
    icon_path = Path(__file__).parent / "assets" / "icon.png"
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))
    
    sys.exit(app.run())

if __name__ == "__main__":
    main()
