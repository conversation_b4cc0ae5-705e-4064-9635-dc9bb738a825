# -*- coding: utf-8 -*-
"""
CyberTrap For3on - Main Window UI
Main interface for the RAT server
"""

import sys
import os
import json
import base64
from datetime import datetime
from pathlib import Path

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from shared.protocol import MessageType
from ui.client_control_window import ClientControlWindow

class MainWindow(QMainWindow):
    """Main window for CyberTrap For3on server"""
    
    # Signals
    start_server_signal = pyqtSignal()
    stop_server_signal = pyqtSignal()
    
    def __init__(self, server):
        super().__init__()
        
        self.server = server
        self.client_windows = {}  # client_id -> ClientControlWindow
        
        self.init_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.setup_status_bar()
        
        # Set window properties
        self.setWindowTitle("CyberTrap For3on - Advanced RAT Server")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # Center window
        self.center_window()
        
    def init_ui(self):
        """Initialize the user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Header with logo and title
        header_widget = self.create_header()
        main_layout.addWidget(header_widget)
        
        # Main content area
        content_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(content_splitter)
        
        # Left panel - Client list
        left_panel = self.create_client_panel()
        content_splitter.addWidget(left_panel)
        
        # Right panel - Logs and info
        right_panel = self.create_info_panel()
        content_splitter.addWidget(right_panel)
        
        # Set splitter proportions
        content_splitter.setSizes([800, 600])
        
    def create_header(self):
        """Create header with logo and title"""
        header_widget = QWidget()
        header_widget.setFixedHeight(80)
        header_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1e3c72, stop:1 #2a5298);
                border-bottom: 2px solid #42a5f5;
            }
        """)
        
        layout = QHBoxLayout(header_widget)
        
        # Logo (placeholder)
        logo_label = QLabel()
        logo_label.setFixedSize(64, 64)
        logo_label.setStyleSheet("""
            QLabel {
                background-color: #42a5f5;
                border-radius: 32px;
                border: 2px solid white;
            }
        """)
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setText("CT")
        logo_label.setFont(QFont("Arial", 20, QFont.Bold))
        layout.addWidget(logo_label)
        
        # Title and subtitle
        title_layout = QVBoxLayout()
        
        title_label = QLabel("CyberTrap For3on")
        title_label.setFont(QFont("Arial", 24, QFont.Bold))
        title_label.setStyleSheet("color: white; background: transparent;")
        title_layout.addWidget(title_label)
        
        subtitle_label = QLabel("Advanced Remote Access Tool for Cybersecurity Defense")
        subtitle_label.setFont(QFont("Arial", 12))
        subtitle_label.setStyleSheet("color: #e0e0e0; background: transparent;")
        title_layout.addWidget(subtitle_label)
        
        layout.addLayout(title_layout)
        layout.addStretch()
        
        # Server status
        self.server_status_label = QLabel("Server: Stopped")
        self.server_status_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.server_status_label.setStyleSheet("color: #ff5252; background: transparent;")
        layout.addWidget(self.server_status_label)
        
        return header_widget
    
    def create_client_panel(self):
        """Create client list panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Panel title
        title_label = QLabel("Connected Clients")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setStyleSheet("color: #42a5f5; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # Client tree
        self.client_tree = QTreeWidget()
        self.client_tree.setHeaderLabels([
            "Client", "IP Address", "OS", "User", "Status", "Connected"
        ])
        
        # Set column widths
        self.client_tree.setColumnWidth(0, 150)
        self.client_tree.setColumnWidth(1, 120)
        self.client_tree.setColumnWidth(2, 100)
        self.client_tree.setColumnWidth(3, 100)
        self.client_tree.setColumnWidth(4, 80)
        self.client_tree.setColumnWidth(5, 120)
        
        # Connect double-click event
        self.client_tree.itemDoubleClicked.connect(self.on_client_double_click)
        
        layout.addWidget(self.client_tree)
        
        # Client actions
        actions_layout = QHBoxLayout()
        
        self.control_button = QPushButton("Control Client")
        self.control_button.setIcon(self.style().standardIcon(QStyle.SP_ComputerIcon))
        self.control_button.clicked.connect(self.open_client_control)
        self.control_button.setEnabled(False)
        actions_layout.addWidget(self.control_button)
        
        self.disconnect_button = QPushButton("Disconnect")
        self.disconnect_button.setIcon(self.style().standardIcon(QStyle.SP_DialogCloseButton))
        self.disconnect_button.clicked.connect(self.disconnect_client)
        self.disconnect_button.setEnabled(False)
        actions_layout.addWidget(self.disconnect_button)
        
        actions_layout.addStretch()
        layout.addLayout(actions_layout)
        
        # Connect selection change
        self.client_tree.itemSelectionChanged.connect(self.on_client_selection_changed)
        
        return panel
    
    def create_info_panel(self):
        """Create information panel with tabs"""
        tab_widget = QTabWidget()
        
        # Server logs tab
        logs_widget = QWidget()
        logs_layout = QVBoxLayout(logs_widget)
        
        logs_label = QLabel("Server Logs")
        logs_label.setFont(QFont("Arial", 12, QFont.Bold))
        logs_layout.addWidget(logs_label)
        
        self.logs_text = QTextEdit()
        self.logs_text.setReadOnly(True)
        self.logs_text.setFont(QFont("Consolas", 10))
        logs_layout.addWidget(self.logs_text)
        
        # Log controls
        log_controls = QHBoxLayout()
        
        clear_logs_button = QPushButton("Clear Logs")
        clear_logs_button.clicked.connect(self.clear_logs)
        log_controls.addWidget(clear_logs_button)
        
        save_logs_button = QPushButton("Save Logs")
        save_logs_button.clicked.connect(self.save_logs)
        log_controls.addWidget(save_logs_button)
        
        log_controls.addStretch()
        logs_layout.addLayout(log_controls)
        
        tab_widget.addTab(logs_widget, "Server Logs")
        
        # Server statistics tab
        stats_widget = self.create_stats_widget()
        tab_widget.addTab(stats_widget, "Statistics")
        
        # Settings tab
        settings_widget = self.create_settings_widget()
        tab_widget.addTab(settings_widget, "Settings")
        
        return tab_widget
    
    def create_stats_widget(self):
        """Create server statistics widget"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Statistics display
        self.stats_text = QTextEdit()
        self.stats_text.setReadOnly(True)
        self.stats_text.setMaximumHeight(200)
        layout.addWidget(self.stats_text)
        
        # Update button
        update_stats_button = QPushButton("Update Statistics")
        update_stats_button.clicked.connect(self.update_statistics)
        layout.addWidget(update_stats_button)
        
        layout.addStretch()
        
        # Update stats initially
        self.update_statistics()
        
        return widget
    
    def create_settings_widget(self):
        """Create settings widget"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Server settings group
        server_group = QGroupBox("Server Settings")
        server_layout = QFormLayout(server_group)
        
        self.host_edit = QLineEdit("0.0.0.0")
        server_layout.addRow("Host:", self.host_edit)
        
        self.port_edit = QLineEdit("4444")
        server_layout.addRow("Port:", self.port_edit)
        
        self.max_clients_edit = QLineEdit("100")
        server_layout.addRow("Max Clients:", self.max_clients_edit)
        
        layout.addWidget(server_group)
        
        # Security settings group
        security_group = QGroupBox("Security Settings")
        security_layout = QVBoxLayout(security_group)
        
        self.encryption_enabled = QCheckBox("Enable Encryption")
        self.encryption_enabled.setChecked(True)
        security_layout.addWidget(self.encryption_enabled)
        
        self.log_connections = QCheckBox("Log All Connections")
        self.log_connections.setChecked(True)
        security_layout.addWidget(self.log_connections)
        
        layout.addWidget(security_group)
        
        # Save settings button
        save_settings_button = QPushButton("Save Settings")
        save_settings_button.clicked.connect(self.save_settings)
        layout.addWidget(save_settings_button)
        
        layout.addStretch()
        
        return widget
    
    def setup_menu_bar(self):
        """Setup menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("File")
        
        start_action = QAction("Start Server", self)
        start_action.setShortcut("Ctrl+S")
        start_action.triggered.connect(self.start_server_signal.emit)
        file_menu.addAction(start_action)
        
        stop_action = QAction("Stop Server", self)
        stop_action.setShortcut("Ctrl+T")
        stop_action.triggered.connect(self.stop_server_signal.emit)
        file_menu.addAction(stop_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("Exit", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Tools menu
        tools_menu = menubar.addMenu("Tools")
        
        payload_builder_action = QAction("Payload Builder", self)
        payload_builder_action.triggered.connect(self.open_payload_builder)
        tools_menu.addAction(payload_builder_action)
        
        # Help menu
        help_menu = menubar.addMenu("Help")
        
        about_action = QAction("About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_toolbar(self):
        """Setup toolbar"""
        toolbar = self.addToolBar("Main")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        # Start server action
        start_action = QAction("Start Server", self)
        start_action.setIcon(self.style().standardIcon(QStyle.SP_MediaPlay))
        start_action.triggered.connect(self.start_server_signal.emit)
        toolbar.addAction(start_action)
        
        # Stop server action
        stop_action = QAction("Stop Server", self)
        stop_action.setIcon(self.style().standardIcon(QStyle.SP_MediaStop))
        stop_action.triggered.connect(self.stop_server_signal.emit)
        toolbar.addAction(stop_action)
        
        toolbar.addSeparator()
        
        # Payload builder action
        payload_action = QAction("Payload Builder", self)
        payload_action.setIcon(self.style().standardIcon(QStyle.SP_FileDialogDetailedView))
        payload_action.triggered.connect(self.open_payload_builder)
        toolbar.addAction(payload_action)
    
    def setup_status_bar(self):
        """Setup status bar"""
        self.status_bar = self.statusBar()
        
        # Client count label
        self.client_count_label = QLabel("Clients: 0")
        self.status_bar.addPermanentWidget(self.client_count_label)
        
        # Server status label
        self.status_label = QLabel("Server: Stopped")
        self.status_bar.addPermanentWidget(self.status_label)
        
        self.status_bar.showMessage("Ready")
    
    def center_window(self):
        """Center window on screen"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )

    def update_server_status(self, running):
        """Update server status display"""
        if running:
            self.server_status_label.setText("Server: Running")
            self.server_status_label.setStyleSheet("color: #4caf50; background: transparent;")
            self.status_label.setText("Server: Running")
        else:
            self.server_status_label.setText("Server: Stopped")
            self.server_status_label.setStyleSheet("color: #ff5252; background: transparent;")
            self.status_label.setText("Server: Stopped")

    def add_client(self, client_id, client_info):
        """Add client to the list"""
        item = QTreeWidgetItem(self.client_tree)

        # Set client data
        display_name = client_info.get_display_name()
        item.setText(0, display_name)
        item.setText(1, client_info.address[0])
        item.setText(2, client_info.os_info[:20] + "..." if len(client_info.os_info) > 20 else client_info.os_info)
        item.setText(3, client_info.username)
        item.setText(4, "Online")
        item.setText(5, datetime.fromtimestamp(client_info.connected_time).strftime("%H:%M:%S"))

        # Store client ID
        item.setData(0, Qt.UserRole, client_id)

        # Set icon based on OS
        if "Windows" in client_info.os_info:
            item.setIcon(0, self.style().standardIcon(QStyle.SP_ComputerIcon))
        else:
            item.setIcon(0, self.style().standardIcon(QStyle.SP_DesktopIcon))

        # Set colors
        if client_info.is_admin:
            item.setForeground(0, QColor("#ff9800"))  # Orange for admin
        else:
            item.setForeground(0, QColor("#4caf50"))  # Green for normal user

        self.update_client_count()

    def remove_client(self, client_id):
        """Remove client from the list"""
        for i in range(self.client_tree.topLevelItemCount()):
            item = self.client_tree.topLevelItem(i)
            if item.data(0, Qt.UserRole) == client_id:
                self.client_tree.takeTopLevelItem(i)
                break

        # Close client control window if open
        if client_id in self.client_windows:
            self.client_windows[client_id].close()
            del self.client_windows[client_id]

        self.update_client_count()

    def update_client_count(self):
        """Update client count display"""
        count = self.client_tree.topLevelItemCount()
        self.client_count_label.setText(f"Clients: {count}")

    def on_client_selection_changed(self):
        """Handle client selection change"""
        selected_items = self.client_tree.selectedItems()
        has_selection = len(selected_items) > 0

        self.control_button.setEnabled(has_selection)
        self.disconnect_button.setEnabled(has_selection)

    def on_client_double_click(self, item, column):
        """Handle client double-click"""
        self.open_client_control()

    def open_client_control(self):
        """Open client control window"""
        selected_items = self.client_tree.selectedItems()
        if not selected_items:
            return

        item = selected_items[0]
        client_id = item.data(0, Qt.UserRole)

        if client_id in self.client_windows:
            # Bring existing window to front
            self.client_windows[client_id].raise_()
            self.client_windows[client_id].activateWindow()
        else:
            # Create new control window
            client_info = self.server.clients.get(client_id)
            if client_info:
                control_window = ClientControlWindow(client_id, client_info, self.server)
                control_window.show()
                self.client_windows[client_id] = control_window

                # Connect window closed signal
                control_window.window_closed.connect(
                    lambda cid=client_id: self.client_windows.pop(cid, None)
                )

    def disconnect_client(self):
        """Disconnect selected client"""
        selected_items = self.client_tree.selectedItems()
        if not selected_items:
            return

        item = selected_items[0]
        client_id = item.data(0, Qt.UserRole)

        reply = QMessageBox.question(
            self,
            "Disconnect Client",
            f"Are you sure you want to disconnect client {item.text(0)}?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.server.disconnect_client(client_id)

    def handle_client_message(self, client_id, message):
        """Handle message from client"""
        # Update client control window if open
        if client_id in self.client_windows:
            self.client_windows[client_id].handle_message(message)

    def add_log_message(self, message, level="INFO"):
        """Add log message to display"""
        color_map = {
            "INFO": "#ffffff",
            "WARNING": "#ff9800",
            "ERROR": "#f44336",
            "SUCCESS": "#4caf50"
        }

        color = color_map.get(level, "#ffffff")

        # Format message with color
        formatted_message = f'<span style="color: {color};">{message}</span>'

        self.logs_text.append(formatted_message)

        # Auto-scroll to bottom
        scrollbar = self.logs_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def clear_logs(self):
        """Clear log display"""
        self.logs_text.clear()

    def save_logs(self):
        """Save logs to file"""
        filename, _ = QFileDialog.getSaveFileName(
            self,
            "Save Logs",
            f"cybertrap_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            "Text Files (*.txt);;All Files (*)"
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.logs_text.toPlainText())

                QMessageBox.information(self, "Success", "Logs saved successfully!")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to save logs: {str(e)}")

    def update_statistics(self):
        """Update server statistics"""
        stats = self.server.get_server_stats()

        stats_text = f"""
Server Statistics:
==================

Total Clients: {stats['total_clients']}
Active Clients: {stats['active_clients']}
Server Host: {stats['host']}
Server Port: {stats['port']}
Server Running: {stats['running']}
Uptime: {stats['server_uptime']:.2f} seconds

Last Updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """

        self.stats_text.setPlainText(stats_text.strip())

    def save_settings(self):
        """Save application settings"""
        # This would save settings to a config file
        QMessageBox.information(self, "Settings", "Settings saved successfully!")

    def open_payload_builder(self):
        """Open payload builder dialog"""
        from ui.payload_builder_dialog import PayloadBuilderDialog

        dialog = PayloadBuilderDialog(self)
        dialog.exec_()

    def show_about(self):
        """Show about dialog"""
        about_text = """
<h2>CyberTrap For3on</h2>
<p><b>Version:</b> 1.0.0</p>
<p><b>Author:</b> For3on Security Team</p>
<p><b>Purpose:</b> Advanced Remote Access Tool for Cybersecurity Defense</p>

<p>This tool is designed for legitimate cybersecurity defense purposes only.</p>
<p>Use responsibly and in accordance with applicable laws and regulations.</p>

<p><b>Features:</b></p>
<ul>
<li>Encrypted client-server communication</li>
<li>Comprehensive system information gathering</li>
<li>Remote command execution</li>
<li>File system management</li>
<li>Screen capture and monitoring</li>
<li>Keylogger functionality</li>
<li>Password extraction</li>
<li>Audio/video capture</li>
</ul>
        """

        QMessageBox.about(self, "About CyberTrap For3on", about_text)
