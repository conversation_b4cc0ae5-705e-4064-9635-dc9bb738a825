#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CyberTrap For3on - Setup Script
Installation and setup script for CyberTrap For3on
"""

import sys
import os
import subprocess
import platform
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    
    print(f"✅ Python version: {sys.version}")
    return True

def check_platform():
    """Check if platform is supported"""
    system = platform.system()
    print(f"🖥️  Platform: {system} {platform.release()}")
    
    if system != "Windows":
        print("⚠️  Warning: This tool is optimized for Windows")
        print("   Some features may not work on other platforms")
    
    return True

def install_requirements():
    """Install Python requirements"""
    print("📦 Installing Python dependencies...")
    
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    if not requirements_file.exists():
        print("❌ requirements.txt not found")
        return False
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def check_optional_tools():
    """Check for optional tools"""
    print("🔧 Checking optional tools...")
    
    tools = {
        "pyinstaller": "For payload compilation",
        "upx": "For executable compression"
    }
    
    for tool, description in tools.items():
        try:
            subprocess.run([tool, "--version"], 
                         capture_output=True, check=True)
            print(f"✅ {tool}: Available ({description})")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print(f"⚠️  {tool}: Not found ({description})")

def create_directories():
    """Create necessary directories"""
    print("📁 Creating directories...")
    
    directories = [
        "logs",
        "output",
        "server/assets"
    ]
    
    base_dir = Path(__file__).parent
    
    for directory in directories:
        dir_path = base_dir / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ Created: {directory}")

def setup_configuration():
    """Setup initial configuration"""
    print("⚙️  Setting up configuration...")
    
    # Import and initialize configuration
    try:
        sys.path.append(str(Path(__file__).parent))
        from shared.config import Config
        
        # Create directories
        Config.create_directories()
        
        # Save default configuration
        Config.save_config()
        
        print("✅ Configuration initialized")
        return True
    except Exception as e:
        print(f"❌ Failed to setup configuration: {e}")
        return False

def run_tests():
    """Run basic tests"""
    print("🧪 Running basic tests...")
    
    try:
        # Test imports
        sys.path.append(str(Path(__file__).parent))
        
        from shared.encryption import crypto_manager
        from shared.protocol import Protocol
        from shared.config import Config
        
        # Test encryption
        crypto_manager.generate_rsa_keys()
        crypto_manager.generate_aes_key()
        
        test_data = "Hello, CyberTrap!"
        encrypted = crypto_manager.encrypt_data(test_data)
        decrypted = crypto_manager.decrypt_data(encrypted)
        
        if decrypted == test_data:
            print("✅ Encryption test passed")
        else:
            print("❌ Encryption test failed")
            return False
        
        print("✅ All tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Tests failed: {e}")
        return False

def print_usage_info():
    """Print usage information"""
    print("\n" + "=" * 60)
    print("🎉 CyberTrap For3on Setup Complete!")
    print("=" * 60)
    print()
    print("📖 Usage:")
    print("   🖥️  Start Server:  python run_server.py")
    print("   🔧 Test Client:   python run_client.py")
    print("   📦 Build Payload: Use the GUI Payload Builder")
    print()
    print("📁 Important Files:")
    print("   📋 README.md     - Complete documentation")
    print("   ⚙️  shared/config.py - Configuration settings")
    print("   📊 logs/         - Application logs")
    print("   📦 output/       - Generated payloads")
    print()
    print("⚠️  Legal Notice:")
    print("   This tool is for educational and legitimate")
    print("   cybersecurity defense purposes only!")
    print()
    print("🔗 For more information, see README.md")
    print("=" * 60)

def main():
    """Main setup function"""
    print("🔥 CyberTrap For3on - Setup Script")
    print("=" * 50)
    print("🛠️  Setting up CyberTrap For3on...")
    print()
    
    # Check requirements
    if not check_python_version():
        sys.exit(1)
    
    if not check_platform():
        sys.exit(1)
    
    # Install dependencies
    if not install_requirements():
        print("❌ Setup failed: Could not install dependencies")
        sys.exit(1)
    
    # Check optional tools
    check_optional_tools()
    
    # Create directories
    create_directories()
    
    # Setup configuration
    if not setup_configuration():
        print("❌ Setup failed: Could not setup configuration")
        sys.exit(1)
    
    # Run tests
    if not run_tests():
        print("❌ Setup failed: Tests failed")
        sys.exit(1)
    
    # Print usage information
    print_usage_info()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Setup failed with error: {e}")
        sys.exit(1)
