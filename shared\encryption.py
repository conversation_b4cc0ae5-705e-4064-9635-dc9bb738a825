# -*- coding: utf-8 -*-
"""
CyberTrap For3on - Encryption Module
Advanced AES + RSA Encryption for Secure Communications
"""

import os
import base64
import json
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.backends import default_backend

class CryptoManager:
    """Advanced encryption manager for secure communications"""
    
    def __init__(self):
        self.backend = default_backend()
        self.rsa_private_key = None
        self.rsa_public_key = None
        self.aes_key = None
        
    def generate_rsa_keys(self, key_size=2048):
        """Generate RSA key pair"""
        self.rsa_private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=key_size,
            backend=self.backend
        )
        self.rsa_public_key = self.rsa_private_key.public_key()
        
    def get_public_key_pem(self):
        """Get public key in PEM format"""
        if not self.rsa_public_key:
            self.generate_rsa_keys()
            
        return self.rsa_public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
    
    def load_public_key_pem(self, pem_data):
        """Load public key from PEM data"""
        self.rsa_public_key = serialization.load_pem_public_key(
            pem_data,
            backend=self.backend
        )
    
    def generate_aes_key(self):
        """Generate AES-256 key"""
        self.aes_key = os.urandom(32)  # 256-bit key
        return self.aes_key
    
    def encrypt_aes_key_with_rsa(self, aes_key=None):
        """Encrypt AES key with RSA public key"""
        if not aes_key:
            aes_key = self.aes_key
        if not self.rsa_public_key:
            raise ValueError("RSA public key not available")
            
        encrypted_key = self.rsa_public_key.encrypt(
            aes_key,
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
        return base64.b64encode(encrypted_key).decode('utf-8')
    
    def decrypt_aes_key_with_rsa(self, encrypted_key_b64):
        """Decrypt AES key with RSA private key"""
        if not self.rsa_private_key:
            raise ValueError("RSA private key not available")
            
        encrypted_key = base64.b64decode(encrypted_key_b64.encode('utf-8'))
        aes_key = self.rsa_private_key.decrypt(
            encrypted_key,
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
        self.aes_key = aes_key
        return aes_key
    
    def encrypt_data(self, data):
        """Encrypt data with AES-256-CBC"""
        if not self.aes_key:
            self.generate_aes_key()
            
        # Convert string to bytes if necessary
        if isinstance(data, str):
            data = data.encode('utf-8')
        elif isinstance(data, dict) or isinstance(data, list):
            data = json.dumps(data, ensure_ascii=False).encode('utf-8')
            
        # Generate random IV
        iv = os.urandom(16)
        
        # Pad data to multiple of 16 bytes
        padding_length = 16 - (len(data) % 16)
        padded_data = data + bytes([padding_length] * padding_length)
        
        # Encrypt
        cipher = Cipher(
            algorithms.AES(self.aes_key),
            modes.CBC(iv),
            backend=self.backend
        )
        encryptor = cipher.encryptor()
        encrypted_data = encryptor.update(padded_data) + encryptor.finalize()
        
        # Combine IV and encrypted data
        result = iv + encrypted_data
        return base64.b64encode(result).decode('utf-8')
    
    def decrypt_data(self, encrypted_data_b64):
        """Decrypt data with AES-256-CBC"""
        if not self.aes_key:
            raise ValueError("AES key not available")
            
        # Decode base64
        encrypted_data = base64.b64decode(encrypted_data_b64.encode('utf-8'))
        
        # Extract IV and encrypted data
        iv = encrypted_data[:16]
        encrypted_data = encrypted_data[16:]
        
        # Decrypt
        cipher = Cipher(
            algorithms.AES(self.aes_key),
            modes.CBC(iv),
            backend=self.backend
        )
        decryptor = cipher.decryptor()
        padded_data = decryptor.update(encrypted_data) + decryptor.finalize()
        
        # Remove padding
        padding_length = padded_data[-1]
        data = padded_data[:-padding_length]
        
        try:
            # Try to decode as UTF-8 string
            return data.decode('utf-8')
        except UnicodeDecodeError:
            # Return raw bytes if not valid UTF-8
            return data
    
    def create_secure_message(self, message_type, data):
        """Create encrypted message with metadata"""
        message = {
            "type": message_type,
            "data": data,
            "timestamp": __import__('time').time()
        }
        
        encrypted_message = self.encrypt_data(message)
        
        return {
            "encrypted": True,
            "payload": encrypted_message
        }
    
    def parse_secure_message(self, encrypted_message):
        """Parse and decrypt secure message"""
        if not encrypted_message.get("encrypted"):
            return encrypted_message
            
        decrypted_data = self.decrypt_data(encrypted_message["payload"])
        
        try:
            return json.loads(decrypted_data)
        except json.JSONDecodeError:
            return {"type": "raw", "data": decrypted_data}

# Global crypto manager instance
crypto_manager = CryptoManager()
