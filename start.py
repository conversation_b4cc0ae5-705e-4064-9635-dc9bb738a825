#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CyberTrap For3on - Main Launcher
Universal launcher for all CyberTrap components
"""

import sys
import os
import subprocess
import time
from pathlib import Path

def print_banner():
    """Print CyberTrap banner"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║   ██████╗██╗   ██╗██████╗ ███████╗██████╗ ████████╗██████╗  █████╗ ██████╗   ║
║  ██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝██╔══██╗╚══██╔══╝██╔══██╗██╔══██╗██╔══██╗  ║
║  ██║      ╚████╔╝ ██████╔╝█████╗  ██████╔╝   ██║   ██████╔╝███████║██████╔╝  ║
║  ██║       ╚██╔╝  ██╔══██╗██╔══╝  ██╔══██╗   ██║   ██╔══██╗██╔══██║██╔═══╝   ║
║  ╚██████╗   ██║   ██████╔╝███████╗██║  ██║   ██║   ██║  ██║██║  ██║██║       ║
║   ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝   ╚═╝   ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝       ║
║                                                                              ║
║                              FOR3ON SECURITY TEAM                           ║
║                     Advanced RAT for Cybersecurity Defense                  ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
"""
    print(banner)

def show_main_menu():
    """Show main menu"""
    print("\n🎯 Choose your action:")
    print("=" * 50)
    print()
    print("🚀 Quick Start:")
    print("  1. Quick Demo (Test core functionality)")
    print("  2. Install Dependencies")
    print("  3. Full System Test")
    print()
    print("🖥️  Server Options:")
    print("  4. Start Console Server")
    print("  5. Start GUI Server (requires PyQt5)")
    print()
    print("🔧 Client Options:")
    print("  6. Start Simple Client")
    print("  7. Start Full Client")
    print("  8. Build Custom Payload")
    print()
    print("🛠️  Development Tools:")
    print("  9. Development Tools Menu")
    print(" 10. Connection Test")
    print(" 11. Clean Project")
    print()
    print("📚 Documentation:")
    print(" 12. View Quick Start Guide")
    print(" 13. View Full Documentation")
    print()
    print(" 0. Exit")
    print()

def run_command(command, description=""):
    """Run a command with error handling"""
    if description:
        print(f"🚀 {description}...")
    
    try:
        if isinstance(command, list):
            result = subprocess.run(command, check=True)
        else:
            result = subprocess.run([sys.executable, command], check=True)
        
        print("✅ Command completed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Command failed with exit code {e.returncode}")
        return False
    except FileNotFoundError:
        print(f"❌ File not found: {command}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def show_dev_menu():
    """Show development tools menu"""
    print("\n🛠️  Development Tools")
    print("=" * 30)
    print("1. Clean Project")
    print("2. Check Dependencies")
    print("3. Run Tests")
    print("4. Build Executable")
    print("5. Create Release")
    print("6. Project Statistics")
    print("7. Back to Main Menu")
    print()
    
    choice = input("Enter your choice (1-7): ").strip()
    
    if choice == "1":
        run_command("dev_tools.py clean", "Cleaning project")
    elif choice == "2":
        run_command("dev_tools.py deps", "Checking dependencies")
    elif choice == "3":
        run_command("dev_tools.py test", "Running tests")
    elif choice == "4":
        run_command("dev_tools.py build", "Building executable")
    elif choice == "5":
        version = input("Enter version (default: 1.0.0): ").strip() or "1.0.0"
        run_command(f"dev_tools.py release {version}", f"Creating release v{version}")
    elif choice == "6":
        run_command("dev_tools.py stats", "Generating statistics")
    elif choice == "7":
        return
    else:
        print("❌ Invalid choice")

def show_documentation(doc_type):
    """Show documentation"""
    docs = {
        "quickstart": "QUICKSTART.md",
        "readme": "README.md",
        "install": "INSTALL.md"
    }
    
    doc_file = docs.get(doc_type)
    if not doc_file:
        print("❌ Documentation not found")
        return
    
    doc_path = Path(doc_file)
    if not doc_path.exists():
        print(f"❌ Documentation file not found: {doc_file}")
        return
    
    try:
        with open(doc_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Show first part of documentation
        lines = content.split('\n')
        for i, line in enumerate(lines[:50]):  # Show first 50 lines
            print(line)
        
        if len(lines) > 50:
            print(f"\n... ({len(lines) - 50} more lines)")
            print(f"📖 Read full documentation: {doc_file}")
        
    except Exception as e:
        print(f"❌ Error reading documentation: {e}")

def check_file_exists(filename):
    """Check if file exists"""
    return Path(filename).exists()

def main():
    """Main launcher function"""
    print_banner()
    
    # Check if we're in the right directory
    if not check_file_exists("shared/config.py"):
        print("❌ Error: Please run this script from the CyberTrap project root directory")
        print("💡 Make sure you're in the directory containing 'shared/', 'server/', 'client/' folders")
        return
    
    while True:
        show_main_menu()
        
        try:
            choice = input("Enter your choice (0-13): ").strip()
            
            if choice == "0":
                print("👋 Thank you for using CyberTrap For3on!")
                print("🛡️  Stay safe and hack ethically!")
                break
            
            elif choice == "1":
                run_command("quick_demo.py", "Running quick demo")
            
            elif choice == "2":
                run_command("install_basic.py", "Installing dependencies")
            
            elif choice == "3":
                run_command("test_full_system.py", "Running full system test")
            
            elif choice == "4":
                print("🖥️  Starting console server...")
                print("💡 Use 'start' command to begin listening for connections")
                run_command("server_console.py", "Starting console server")
            
            elif choice == "5":
                if check_file_exists("run_server.py"):
                    run_command("run_server.py", "Starting GUI server")
                else:
                    print("❌ GUI server not available")
                    print("💡 Try console server instead (option 4)")
            
            elif choice == "6":
                host = input("Server host (default: 127.0.0.1): ").strip() or "127.0.0.1"
                port = input("Server port (default: 4444): ").strip() or "4444"
                
                print(f"🔄 Connecting to {host}:{port}...")
                run_command(f"client_simple.py {host} {port}", "Starting simple client")
            
            elif choice == "7":
                if check_file_exists("run_client.py"):
                    run_command("run_client.py", "Starting full client")
                else:
                    print("❌ Full client not available")
                    print("💡 Try simple client instead (option 6)")
            
            elif choice == "8":
                print("🔨 Payload builder requires GUI server")
                print("💡 Start GUI server (option 5) and use Tools > Payload Builder")
            
            elif choice == "9":
                show_dev_menu()
            
            elif choice == "10":
                print("🔍 Connection Test Options:")
                print("1. Test server port")
                print("2. Start test server")
                print("3. Test client connection")
                print("4. Network diagnostics")
                
                test_choice = input("Choose test (1-4): ").strip()
                
                if test_choice == "1":
                    host = input("Host (default: 127.0.0.1): ").strip() or "127.0.0.1"
                    port = input("Port (default: 4444): ").strip() or "4444"
                    run_command(f"test_connection.py check {host} {port}", "Testing port")
                elif test_choice == "2":
                    run_command("test_connection.py server", "Starting test server")
                elif test_choice == "3":
                    host = input("Host (default: 127.0.0.1): ").strip() or "127.0.0.1"
                    port = input("Port (default: 4444): ").strip() or "4444"
                    run_command(f"test_connection.py client {host} {port}", "Testing client")
                elif test_choice == "4":
                    run_command("test_connection.py diag", "Running diagnostics")
            
            elif choice == "11":
                run_command("dev_tools.py clean", "Cleaning project")
            
            elif choice == "12":
                show_documentation("quickstart")
            
            elif choice == "13":
                show_documentation("readme")
            
            else:
                print("❌ Invalid choice. Please enter 0-13.")
            
            if choice != "0":
                print("\n" + "="*60)
                input("Press Enter to continue...")
                print()
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            print("Please try again or choose a different option.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Launcher interrupted")
    except Exception as e:
        print(f"❌ Launcher error: {e}")
        import traceback
        traceback.print_exc()
