#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CyberTrap For3on - Connection Test
Test connectivity between server and client
"""

import socket
import time
import threading
import sys
from pathlib import Path

def test_server_port(host="127.0.0.1", port=4444, timeout=5):
    """Test if server port is accessible"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            return True, f"Port {port} is open on {host}"
        else:
            return False, f"Port {port} is closed on {host}"
            
    except Exception as e:
        return False, f"Connection test failed: {str(e)}"

def start_test_server(host="127.0.0.1", port=4444):
    """Start a simple test server"""
    try:
        server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        server_socket.bind((host, port))
        server_socket.listen(1)
        
        print(f"✅ Test server started on {host}:{port}")
        print("🔄 Waiting for connections...")
        
        while True:
            try:
                client_socket, address = server_socket.accept()
                print(f"📡 Connection from {address[0]}:{address[1]}")
                
                # Send welcome message
                welcome_msg = b"CyberTrap Test Server - Connection Successful!\n"
                client_socket.send(welcome_msg)
                
                # Close connection
                client_socket.close()
                print(f"✅ Connection test completed for {address[0]}")
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"❌ Server error: {e}")
                
    except Exception as e:
        print(f"❌ Failed to start test server: {e}")
        return False
    finally:
        try:
            server_socket.close()
        except:
            pass
        print("🛑 Test server stopped")
    
    return True

def test_client_connection(host="127.0.0.1", port=4444):
    """Test client connection to server"""
    try:
        print(f"🔄 Testing connection to {host}:{port}...")
        
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect((host, port))
        
        # Receive welcome message
        response = sock.recv(1024)
        print(f"📨 Server response: {response.decode().strip()}")
        
        sock.close()
        print("✅ Client connection test successful")
        return True
        
    except Exception as e:
        print(f"❌ Client connection test failed: {e}")
        return False

def run_network_diagnostics():
    """Run network diagnostics"""
    print("🔍 Running network diagnostics...")
    print("-" * 50)
    
    # Test localhost connectivity
    print("1. Testing localhost connectivity...")
    success, message = test_server_port("127.0.0.1", 4444)
    print(f"   {message}")
    
    # Test network interfaces
    print("\n2. Checking network interfaces...")
    try:
        import psutil
        interfaces = psutil.net_if_addrs()
        
        for interface, addrs in interfaces.items():
            for addr in addrs:
                if addr.family == socket.AF_INET:
                    print(f"   {interface}: {addr.address}")
    except ImportError:
        print("   psutil not available - install with: pip install psutil")
    
    # Test DNS resolution
    print("\n3. Testing DNS resolution...")
    test_hosts = ["google.com", "github.com", "python.org"]
    
    for host in test_hosts:
        try:
            ip = socket.gethostbyname(host)
            print(f"   {host} -> {ip} ✅")
        except Exception as e:
            print(f"   {host} -> Failed: {e} ❌")
    
    print("-" * 50)

def main():
    """Main test function"""
    print("🔥 CyberTrap For3on - Connection Test")
    print("=" * 50)
    
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python test_connection.py server [host] [port]  - Start test server")
        print("  python test_connection.py client [host] [port]  - Test client connection")
        print("  python test_connection.py diag                  - Run diagnostics")
        print("  python test_connection.py check [host] [port]   - Check port status")
        print()
        print("Examples:")
        print("  python test_connection.py server")
        print("  python test_connection.py client 127.0.0.1 4444")
        print("  python test_connection.py diag")
        return
    
    mode = sys.argv[1].lower()
    
    # Parse host and port
    host = sys.argv[2] if len(sys.argv) > 2 else "127.0.0.1"
    port = int(sys.argv[3]) if len(sys.argv) > 3 else 4444
    
    if mode == "server":
        print(f"🚀 Starting test server on {host}:{port}")
        print("Press Ctrl+C to stop")
        start_test_server(host, port)
        
    elif mode == "client":
        print(f"🔄 Testing client connection to {host}:{port}")
        test_client_connection(host, port)
        
    elif mode == "diag":
        run_network_diagnostics()
        
    elif mode == "check":
        print(f"🔍 Checking port {port} on {host}")
        success, message = test_server_port(host, port)
        print(f"Result: {message}")
        
        if success:
            print("✅ Port is accessible")
        else:
            print("❌ Port is not accessible")
            print("\nTroubleshooting tips:")
            print("- Check if server is running")
            print("- Verify firewall settings")
            print("- Ensure port is not blocked")
            print("- Try different port number")
    
    else:
        print(f"❌ Unknown mode: {mode}")
        print("Use 'server', 'client', 'diag', or 'check'")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)
