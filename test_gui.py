#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
For3on CyberTrap - GUI Test
Simple test for the GUI components
"""

import sys
import os
from pathlib import Path

def test_imports():
    """Test if all GUI components can be imported"""
    print("🧪 Testing GUI Components...")
    print("-" * 30)
    
    # Add GUI directory to path
    gui_dir = Path(__file__).parent / "gui"
    sys.path.insert(0, str(gui_dir))
    
    try:
        print("📦 Testing PyQt5...")
        from PyQt5.QtWidgets import QApplication, QWidget
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QIcon
        print("   ✅ PyQt5 imported successfully")
        
        print("🎨 Testing custom components...")
        from splash_screen import SplashScreen, AnimatedLogo
        print("   ✅ Splash screen components imported")
        
        from main_window import MainWindow, VictimCard, ControlPanel
        print("   ✅ Main window components imported")
        
        from resources import IconFactory, ThemeManager
        print("   ✅ Resources and themes imported")
        
        print("🚀 Testing application...")
        from cybertrap_app import CyberTrapApplication
        print("   ✅ Main application imported")
        
        print("\n✅ All GUI components imported successfully!")
        return True
        
    except ImportError as e:
        print(f"   ❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        return False

def test_simple_window():
    """Test creating a simple window"""
    print("\n🖼️  Testing Simple Window Creation...")
    print("-" * 35)
    
    try:
        from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
        from PyQt5.QtCore import Qt, QTimer
        from PyQt5.QtGui import QFont
        
        # Create application
        app = QApplication(sys.argv)
        
        # Create test window
        window = QMainWindow()
        window.setWindowTitle("For3on CyberTrap - GUI Test")
        window.setGeometry(100, 100, 600, 400)
        
        # Central widget
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Title
        title = QLabel("🔥 For3on CyberTrap")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 24, QFont.Bold))
        title.setStyleSheet("color: #00ffff; margin: 20px;")
        layout.addWidget(title)
        
        # Subtitle
        subtitle = QLabel("Professional RAT Interface - GUI Test")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setFont(QFont("Arial", 14))
        subtitle.setStyleSheet("color: #ffffff; margin: 10px;")
        layout.addWidget(subtitle)
        
        # Status
        status = QLabel("✅ GUI Components Working!")
        status.setAlignment(Qt.AlignCenter)
        status.setFont(QFont("Arial", 16, QFont.Bold))
        status.setStyleSheet("color: #68d391; margin: 20px;")
        layout.addWidget(status)
        
        # Apply dark theme
        window.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1a202c, stop:1 #2d3748);
            }
            QWidget {
                background: transparent;
            }
        """)
        
        # Show window
        window.show()
        
        print("   ✅ Test window created successfully")
        print("   🖼️  Window should be visible now")
        
        # Auto-close after 3 seconds
        QTimer.singleShot(3000, app.quit)
        
        # Run for a short time
        app.exec_()
        
        print("   ✅ Test window closed successfully")
        return True
        
    except Exception as e:
        print(f"   ❌ Window test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🔥 For3on CyberTrap - GUI Test Suite")
    print("=" * 50)
    
    # Test 1: Import components
    if not test_imports():
        print("\n❌ GUI component test failed")
        print("💡 Make sure PyQt5 is installed: pip install PyQt5")
        return 1
    
    # Test 2: Simple window
    if not test_simple_window():
        print("\n❌ Window creation test failed")
        return 1
    
    print("\n🎉 All GUI tests passed!")
    print("✅ The GUI interface is ready to use")
    print("\n🚀 To run the full GUI:")
    print("   python run_gui.py")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
